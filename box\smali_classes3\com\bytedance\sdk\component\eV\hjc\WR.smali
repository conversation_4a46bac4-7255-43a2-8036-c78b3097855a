.class public Lcom/bytedance/sdk/component/eV/hjc/WR;
.super Ljava/lang/Object;


# instance fields
.field private BcC:Ljava/util/concurrent/ExecutorService;

.field private Fj:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/util/List<",
            "Lcom/bytedance/sdk/component/eV/hjc/hjc;",
            ">;>;"
        }
    .end annotation
.end field

.field private Ubf:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Lcom/bytedance/sdk/component/eV/hjc;",
            ">;"
        }
    .end annotation
.end field

.field private WR:Lcom/bytedance/sdk/component/eV/eV;

.field private eV:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Lcom/bytedance/sdk/component/eV/vYf;",
            ">;"
        }
    .end annotation
.end field

.field private final ex:Lcom/bytedance/sdk/component/eV/dG;

.field private hjc:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Lcom/bytedance/sdk/component/eV/rS;",
            ">;"
        }
    .end annotation
.end field

.field private mSE:Lcom/bytedance/sdk/component/eV/Ql;

.field private svN:Lcom/bytedance/sdk/component/eV/UYd;


# direct methods
.method public constructor <init>(Landroid/content/Context;Lcom/bytedance/sdk/component/eV/dG;)V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    new-instance v0, Ljava/util/concurrent/ConcurrentHashMap;

    invoke-direct {v0}, Ljava/util/concurrent/ConcurrentHashMap;-><init>()V

    iput-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/WR;->Fj:Ljava/util/Map;

    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    iput-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/WR;->hjc:Ljava/util/Map;

    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    iput-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/WR;->eV:Ljava/util/Map;

    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    iput-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/WR;->Ubf:Ljava/util/Map;

    invoke-static {p2}, Lcom/bytedance/sdk/component/eV/hjc/BcC;->Fj(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/bytedance/sdk/component/eV/dG;

    iput-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/WR;->ex:Lcom/bytedance/sdk/component/eV/dG;

    invoke-interface {p2}, Lcom/bytedance/sdk/component/eV/dG;->BcC()Lcom/bytedance/sdk/component/eV/ex;

    move-result-object p2

    invoke-static {p1, p2}, Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj;->Fj(Landroid/content/Context;Lcom/bytedance/sdk/component/eV/ex;)V

    return-void
.end method

.method private Ko()Lcom/bytedance/sdk/component/eV/UYd;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/WR;->ex:Lcom/bytedance/sdk/component/eV/dG;

    invoke-interface {v0}, Lcom/bytedance/sdk/component/eV/dG;->Fj()Lcom/bytedance/sdk/component/eV/UYd;

    move-result-object v0

    if-eqz v0, :cond_0

    return-object v0

    :cond_0
    invoke-static {}, Lcom/bytedance/sdk/component/eV/Fj/ex;->Fj()Lcom/bytedance/sdk/component/eV/UYd;

    move-result-object v0

    return-object v0
.end method

.method private UYd()Lcom/bytedance/sdk/component/eV/Ql;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/WR;->ex:Lcom/bytedance/sdk/component/eV/dG;

    invoke-interface {v0}, Lcom/bytedance/sdk/component/eV/dG;->svN()Lcom/bytedance/sdk/component/eV/Ql;

    move-result-object v0

    if-nez v0, :cond_0

    new-instance v0, Lcom/bytedance/sdk/component/eV/hjc/svN;

    invoke-direct {v0}, Lcom/bytedance/sdk/component/eV/hjc/svN;-><init>()V

    :cond_0
    return-object v0
.end method

.method private Ubf(Lcom/bytedance/sdk/component/eV/ex;)Lcom/bytedance/sdk/component/eV/vYf;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/WR;->ex:Lcom/bytedance/sdk/component/eV/dG;

    invoke-interface {v0}, Lcom/bytedance/sdk/component/eV/dG;->Ubf()Lcom/bytedance/sdk/component/eV/vYf;

    move-result-object v0

    if-eqz v0, :cond_0

    return-object v0

    :cond_0
    invoke-interface {p1}, Lcom/bytedance/sdk/component/eV/ex;->ex()I

    move-result p1

    invoke-static {p1}, Lcom/bytedance/sdk/component/eV/hjc/Fj/ex/Ubf;->Fj(I)Lcom/bytedance/sdk/component/eV/vYf;

    move-result-object p1

    return-object p1
.end method

.method private WR(Lcom/bytedance/sdk/component/eV/ex;)Lcom/bytedance/sdk/component/eV/hjc;
    .locals 4

    iget-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/WR;->ex:Lcom/bytedance/sdk/component/eV/dG;

    invoke-interface {v0}, Lcom/bytedance/sdk/component/eV/dG;->WR()Lcom/bytedance/sdk/component/eV/hjc;

    move-result-object v0

    if-eqz v0, :cond_0

    return-object v0

    :cond_0
    new-instance v0, Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/ex;

    invoke-interface {p1}, Lcom/bytedance/sdk/component/eV/ex;->Ubf()Ljava/io/File;

    move-result-object v1

    invoke-interface {p1}, Lcom/bytedance/sdk/component/eV/ex;->Fj()J

    move-result-wide v2

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/eV/hjc/WR;->WR()Ljava/util/concurrent/ExecutorService;

    move-result-object p1

    invoke-direct {v0, v1, v2, v3, p1}, Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/ex;-><init>(Ljava/io/File;JLjava/util/concurrent/ExecutorService;)V

    return-object v0
.end method

.method private eV(Lcom/bytedance/sdk/component/eV/ex;)Lcom/bytedance/sdk/component/eV/rS;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/WR;->ex:Lcom/bytedance/sdk/component/eV/dG;

    invoke-interface {v0}, Lcom/bytedance/sdk/component/eV/dG;->eV()Lcom/bytedance/sdk/component/eV/rS;

    move-result-object v0

    if-eqz v0, :cond_0

    invoke-static {v0}, Lcom/bytedance/sdk/component/eV/hjc/Fj/ex/Fj;->Fj(Lcom/bytedance/sdk/component/eV/rS;)Lcom/bytedance/sdk/component/eV/rS;

    move-result-object p1

    return-object p1

    :cond_0
    invoke-interface {p1}, Lcom/bytedance/sdk/component/eV/ex;->ex()I

    move-result p1

    invoke-static {p1}, Lcom/bytedance/sdk/component/eV/hjc/Fj/ex/Fj;->Fj(I)Lcom/bytedance/sdk/component/eV/rS;

    move-result-object p1

    return-object p1
.end method

.method private mSE()Lcom/bytedance/sdk/component/eV/eV;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/WR;->ex:Lcom/bytedance/sdk/component/eV/dG;

    invoke-interface {v0}, Lcom/bytedance/sdk/component/eV/dG;->hjc()Lcom/bytedance/sdk/component/eV/eV;

    move-result-object v0

    if-nez v0, :cond_0

    invoke-static {}, Lcom/bytedance/sdk/component/eV/ex/ex;->Fj()Lcom/bytedance/sdk/component/eV/eV;

    move-result-object v0

    :cond_0
    return-object v0
.end method

.method private rAx()Ljava/util/concurrent/ExecutorService;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/WR;->ex:Lcom/bytedance/sdk/component/eV/dG;

    invoke-interface {v0}, Lcom/bytedance/sdk/component/eV/dG;->ex()Ljava/util/concurrent/ExecutorService;

    move-result-object v0

    if-eqz v0, :cond_0

    return-object v0

    :cond_0
    invoke-static {}, Lcom/bytedance/sdk/component/eV/Fj/hjc;->Fj()Ljava/util/concurrent/ExecutorService;

    move-result-object v0

    return-object v0
.end method


# virtual methods
.method public BcC()Lcom/bytedance/sdk/component/eV/Ql;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/WR;->mSE:Lcom/bytedance/sdk/component/eV/Ql;

    if-nez v0, :cond_0

    invoke-direct {p0}, Lcom/bytedance/sdk/component/eV/hjc/WR;->UYd()Lcom/bytedance/sdk/component/eV/Ql;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/WR;->mSE:Lcom/bytedance/sdk/component/eV/Ql;

    :cond_0
    iget-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/WR;->mSE:Lcom/bytedance/sdk/component/eV/Ql;

    return-object v0
.end method

.method public Fj(Lcom/bytedance/sdk/component/eV/hjc/hjc;)Lcom/bytedance/sdk/component/eV/hjc/ex/Fj;
    .locals 8

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->eV()Landroid/widget/ImageView$ScaleType;

    move-result-object v0

    if-nez v0, :cond_0

    sget-object v0, Lcom/bytedance/sdk/component/eV/hjc/ex/Fj;->Fj:Landroid/widget/ImageView$ScaleType;

    :cond_0
    move-object v4, v0

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->rAx()Landroid/graphics/Bitmap$Config;

    move-result-object v0

    if-nez v0, :cond_1

    sget-object v0, Lcom/bytedance/sdk/component/eV/hjc/ex/Fj;->ex:Landroid/graphics/Bitmap$Config;

    :cond_1
    move-object v5, v0

    new-instance v0, Lcom/bytedance/sdk/component/eV/hjc/ex/Fj;

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->ex()I

    move-result v2

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->hjc()I

    move-result v3

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->svN()I

    move-result v6

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->BcC()I

    move-result v7

    move-object v1, v0

    invoke-direct/range {v1 .. v7}, Lcom/bytedance/sdk/component/eV/hjc/ex/Fj;-><init>(IILandroid/widget/ImageView$ScaleType;Landroid/graphics/Bitmap$Config;II)V

    return-object v0
.end method

.method public Fj(Ljava/lang/String;)Lcom/bytedance/sdk/component/eV/hjc;
    .locals 1

    new-instance v0, Ljava/io/File;

    invoke-direct {v0, p1}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    invoke-static {v0}, Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj;->Fj(Ljava/io/File;)Lcom/bytedance/sdk/component/eV/ex;

    move-result-object p1

    invoke-virtual {p0, p1}, Lcom/bytedance/sdk/component/eV/hjc/WR;->hjc(Lcom/bytedance/sdk/component/eV/ex;)Lcom/bytedance/sdk/component/eV/hjc;

    move-result-object p1

    return-object p1
.end method

.method public Fj(Lcom/bytedance/sdk/component/eV/ex;)Lcom/bytedance/sdk/component/eV/rS;
    .locals 2

    if-nez p1, :cond_0

    invoke-static {}, Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj;->svN()Lcom/bytedance/sdk/component/eV/ex;

    move-result-object p1

    :cond_0
    invoke-interface {p1}, Lcom/bytedance/sdk/component/eV/ex;->Ubf()Ljava/io/File;

    move-result-object v0

    invoke-virtual {v0}, Ljava/io/File;->toString()Ljava/lang/String;

    move-result-object v0

    iget-object v1, p0, Lcom/bytedance/sdk/component/eV/hjc/WR;->hjc:Ljava/util/Map;

    invoke-interface {v1, v0}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/bytedance/sdk/component/eV/rS;

    if-nez v1, :cond_1

    invoke-direct {p0, p1}, Lcom/bytedance/sdk/component/eV/hjc/WR;->eV(Lcom/bytedance/sdk/component/eV/ex;)Lcom/bytedance/sdk/component/eV/rS;

    move-result-object v1

    iget-object p1, p0, Lcom/bytedance/sdk/component/eV/hjc/WR;->hjc:Ljava/util/Map;

    invoke-interface {p1, v0, v1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    :cond_1
    return-object v1
.end method

.method public Fj()Ljava/util/Collection;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Collection<",
            "Lcom/bytedance/sdk/component/eV/rS;",
            ">;"
        }
    .end annotation

    iget-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/WR;->hjc:Ljava/util/Map;

    invoke-interface {v0}, Ljava/util/Map;->values()Ljava/util/Collection;

    move-result-object v0

    return-object v0
.end method

.method public Ubf()Lcom/bytedance/sdk/component/eV/UYd;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/WR;->svN:Lcom/bytedance/sdk/component/eV/UYd;

    if-nez v0, :cond_0

    invoke-direct {p0}, Lcom/bytedance/sdk/component/eV/hjc/WR;->Ko()Lcom/bytedance/sdk/component/eV/UYd;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/WR;->svN:Lcom/bytedance/sdk/component/eV/UYd;

    :cond_0
    iget-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/WR;->svN:Lcom/bytedance/sdk/component/eV/UYd;

    return-object v0
.end method

.method public WR()Ljava/util/concurrent/ExecutorService;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/WR;->BcC:Ljava/util/concurrent/ExecutorService;

    if-nez v0, :cond_0

    invoke-direct {p0}, Lcom/bytedance/sdk/component/eV/hjc/WR;->rAx()Ljava/util/concurrent/ExecutorService;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/WR;->BcC:Ljava/util/concurrent/ExecutorService;

    :cond_0
    iget-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/WR;->BcC:Ljava/util/concurrent/ExecutorService;

    return-object v0
.end method

.method public eV()Lcom/bytedance/sdk/component/eV/eV;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/WR;->WR:Lcom/bytedance/sdk/component/eV/eV;

    if-nez v0, :cond_0

    invoke-direct {p0}, Lcom/bytedance/sdk/component/eV/hjc/WR;->mSE()Lcom/bytedance/sdk/component/eV/eV;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/WR;->WR:Lcom/bytedance/sdk/component/eV/eV;

    :cond_0
    iget-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/WR;->WR:Lcom/bytedance/sdk/component/eV/eV;

    return-object v0
.end method

.method public ex(Lcom/bytedance/sdk/component/eV/ex;)Lcom/bytedance/sdk/component/eV/vYf;
    .locals 2

    if-nez p1, :cond_0

    invoke-static {}, Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj;->svN()Lcom/bytedance/sdk/component/eV/ex;

    move-result-object p1

    :cond_0
    invoke-interface {p1}, Lcom/bytedance/sdk/component/eV/ex;->Ubf()Ljava/io/File;

    move-result-object v0

    invoke-virtual {v0}, Ljava/io/File;->toString()Ljava/lang/String;

    move-result-object v0

    iget-object v1, p0, Lcom/bytedance/sdk/component/eV/hjc/WR;->eV:Ljava/util/Map;

    invoke-interface {v1, v0}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/bytedance/sdk/component/eV/vYf;

    if-nez v1, :cond_1

    invoke-direct {p0, p1}, Lcom/bytedance/sdk/component/eV/hjc/WR;->Ubf(Lcom/bytedance/sdk/component/eV/ex;)Lcom/bytedance/sdk/component/eV/vYf;

    move-result-object v1

    iget-object p1, p0, Lcom/bytedance/sdk/component/eV/hjc/WR;->eV:Ljava/util/Map;

    invoke-interface {p1, v0, v1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    :cond_1
    return-object v1
.end method

.method public ex()Ljava/util/Collection;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Collection<",
            "Lcom/bytedance/sdk/component/eV/vYf;",
            ">;"
        }
    .end annotation

    iget-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/WR;->eV:Ljava/util/Map;

    invoke-interface {v0}, Ljava/util/Map;->values()Ljava/util/Collection;

    move-result-object v0

    return-object v0
.end method

.method public hjc(Lcom/bytedance/sdk/component/eV/ex;)Lcom/bytedance/sdk/component/eV/hjc;
    .locals 2

    if-nez p1, :cond_0

    invoke-static {}, Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj;->svN()Lcom/bytedance/sdk/component/eV/ex;

    move-result-object p1

    :cond_0
    invoke-interface {p1}, Lcom/bytedance/sdk/component/eV/ex;->Ubf()Ljava/io/File;

    move-result-object v0

    invoke-virtual {v0}, Ljava/io/File;->toString()Ljava/lang/String;

    move-result-object v0

    iget-object v1, p0, Lcom/bytedance/sdk/component/eV/hjc/WR;->Ubf:Ljava/util/Map;

    invoke-interface {v1, v0}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/bytedance/sdk/component/eV/hjc;

    if-nez v1, :cond_1

    invoke-direct {p0, p1}, Lcom/bytedance/sdk/component/eV/hjc/WR;->WR(Lcom/bytedance/sdk/component/eV/ex;)Lcom/bytedance/sdk/component/eV/hjc;

    move-result-object v1

    iget-object p1, p0, Lcom/bytedance/sdk/component/eV/hjc/WR;->Ubf:Ljava/util/Map;

    invoke-interface {p1, v0, v1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    :cond_1
    return-object v1
.end method

.method public hjc()Ljava/util/Collection;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Collection<",
            "Lcom/bytedance/sdk/component/eV/hjc;",
            ">;"
        }
    .end annotation

    iget-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/WR;->Ubf:Ljava/util/Map;

    invoke-interface {v0}, Ljava/util/Map;->values()Ljava/util/Collection;

    move-result-object v0

    return-object v0
.end method

.method public svN()Ljava/util/Map;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/util/List<",
            "Lcom/bytedance/sdk/component/eV/hjc/hjc;",
            ">;>;"
        }
    .end annotation

    iget-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/WR;->Fj:Ljava/util/Map;

    return-object v0
.end method
