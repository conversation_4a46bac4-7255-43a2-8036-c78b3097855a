.class public Lcom/bytedance/sdk/component/eV/hjc/eV;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/sdk/component/eV/rAx;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "<T:",
        "Ljava/lang/Object;",
        ">",
        "Ljava/lang/Object;",
        "Lcom/bytedance/sdk/component/eV/rAx;"
    }
.end annotation


# instance fields
.field private BcC:Z

.field private Fj:Ljava/lang/String;

.field private Ko:Lcom/bytedance/sdk/component/eV/svN;

.field private Ubf:I

.field private WR:I

.field private eV:Ljava/lang/Object;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "TT;"
        }
    .end annotation
.end field

.field private ex:Ljava/lang/String;

.field private hjc:Ljava/lang/Object;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "TT;"
        }
    .end annotation
.end field

.field private mSE:Z

.field private rAx:I

.field private svN:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public Fj(Lcom/bytedance/sdk/component/eV/hjc/hjc;Ljava/lang/Object;)Lcom/bytedance/sdk/component/eV/hjc/eV;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/bytedance/sdk/component/eV/hjc/hjc;",
            "TT;)",
            "Lcom/bytedance/sdk/component/eV/hjc/eV;"
        }
    .end annotation

    iput-object p2, p0, Lcom/bytedance/sdk/component/eV/hjc/eV;->hjc:Ljava/lang/Object;

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->Ubf()Ljava/lang/String;

    move-result-object p2

    iput-object p2, p0, Lcom/bytedance/sdk/component/eV/hjc/eV;->Fj:Ljava/lang/String;

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->Fj()Ljava/lang/String;

    move-result-object p2

    iput-object p2, p0, Lcom/bytedance/sdk/component/eV/hjc/eV;->ex:Ljava/lang/String;

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->ex()I

    move-result p2

    iput p2, p0, Lcom/bytedance/sdk/component/eV/hjc/eV;->Ubf:I

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->hjc()I

    move-result p2

    iput p2, p0, Lcom/bytedance/sdk/component/eV/hjc/eV;->WR:I

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->JW()Z

    move-result p2

    iput-boolean p2, p0, Lcom/bytedance/sdk/component/eV/hjc/eV;->mSE:Z

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->JU()Lcom/bytedance/sdk/component/eV/svN;

    move-result-object p2

    iput-object p2, p0, Lcom/bytedance/sdk/component/eV/hjc/eV;->Ko:Lcom/bytedance/sdk/component/eV/svN;

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->Ql()I

    move-result p1

    iput p1, p0, Lcom/bytedance/sdk/component/eV/hjc/eV;->rAx:I

    return-object p0
.end method

.method public Fj(Lcom/bytedance/sdk/component/eV/hjc/hjc;Ljava/lang/Object;Ljava/util/Map;Z)Lcom/bytedance/sdk/component/eV/hjc/eV;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/bytedance/sdk/component/eV/hjc/hjc;",
            "TT;",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ">;Z)",
            "Lcom/bytedance/sdk/component/eV/hjc/eV;"
        }
    .end annotation

    iput-object p3, p0, Lcom/bytedance/sdk/component/eV/hjc/eV;->svN:Ljava/util/Map;

    iput-boolean p4, p0, Lcom/bytedance/sdk/component/eV/hjc/eV;->BcC:Z

    invoke-virtual {p0, p1, p2}, Lcom/bytedance/sdk/component/eV/hjc/eV;->Fj(Lcom/bytedance/sdk/component/eV/hjc/hjc;Ljava/lang/Object;)Lcom/bytedance/sdk/component/eV/hjc/eV;

    move-result-object p1

    return-object p1
.end method

.method public Fj()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/eV;->ex:Ljava/lang/String;

    return-object v0
.end method

.method public Fj(Ljava/lang/Object;)V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/eV;->hjc:Ljava/lang/Object;

    iput-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/eV;->eV:Ljava/lang/Object;

    iput-object p1, p0, Lcom/bytedance/sdk/component/eV/hjc/eV;->hjc:Ljava/lang/Object;

    return-void
.end method

.method public Ubf()Z
    .locals 1

    iget-boolean v0, p0, Lcom/bytedance/sdk/component/eV/hjc/eV;->BcC:Z

    return v0
.end method

.method public WR()Z
    .locals 1

    iget-boolean v0, p0, Lcom/bytedance/sdk/component/eV/hjc/eV;->mSE:Z

    return v0
.end method

.method public eV()Ljava/util/Map;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    iget-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/eV;->svN:Ljava/util/Map;

    return-object v0
.end method

.method public ex()Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()TT;"
        }
    .end annotation

    iget-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/eV;->hjc:Ljava/lang/Object;

    return-object v0
.end method

.method public hjc()Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()TT;"
        }
    .end annotation

    iget-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/eV;->eV:Ljava/lang/Object;

    return-object v0
.end method

.method public svN()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/component/eV/hjc/eV;->rAx:I

    return v0
.end method
