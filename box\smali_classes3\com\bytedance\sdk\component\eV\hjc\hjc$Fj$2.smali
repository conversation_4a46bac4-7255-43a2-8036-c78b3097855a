.class Lcom/bytedance/sdk/component/eV/hjc/hjc$Fj$2;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/component/eV/hjc/hjc$Fj;->Fj(Lcom/bytedance/sdk/component/eV/rAx;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/component/eV/rAx;

.field final synthetic ex:Lcom/bytedance/sdk/component/eV/hjc/hjc$Fj;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/component/eV/hjc/hjc$Fj;Lcom/bytedance/sdk/component/eV/rAx;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc$Fj$2;->ex:Lcom/bytedance/sdk/component/eV/hjc/hjc$Fj;

    iput-object p2, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc$Fj$2;->Fj:Lcom/bytedance/sdk/component/eV/rAx;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 2

    iget-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc$Fj$2;->ex:Lcom/bytedance/sdk/component/eV/hjc/hjc$Fj;

    invoke-static {v0}, Lcom/bytedance/sdk/component/eV/hjc/hjc$Fj;->Fj(Lcom/bytedance/sdk/component/eV/hjc/hjc$Fj;)Lcom/bytedance/sdk/component/eV/JU;

    move-result-object v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc$Fj$2;->ex:Lcom/bytedance/sdk/component/eV/hjc/hjc$Fj;

    invoke-static {v0}, Lcom/bytedance/sdk/component/eV/hjc/hjc$Fj;->Fj(Lcom/bytedance/sdk/component/eV/hjc/hjc$Fj;)Lcom/bytedance/sdk/component/eV/JU;

    move-result-object v0

    iget-object v1, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc$Fj$2;->Fj:Lcom/bytedance/sdk/component/eV/rAx;

    invoke-interface {v0, v1}, Lcom/bytedance/sdk/component/eV/JU;->Fj(Lcom/bytedance/sdk/component/eV/rAx;)V

    :cond_0
    return-void
.end method
