.class Lcom/bytedance/sdk/component/eV/hjc/hjc$Fj;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/sdk/component/eV/JU;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bytedance/sdk/component/eV/hjc/hjc;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = "Fj"
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/component/eV/hjc/hjc;

.field private ex:Lcom/bytedance/sdk/component/eV/JU;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/component/eV/hjc/hjc;Lcom/bytedance/sdk/component/eV/JU;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc$Fj;->Fj:Lcom/bytedance/sdk/component/eV/hjc/hjc;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p2, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc$Fj;->ex:Lcom/bytedance/sdk/component/eV/JU;

    return-void
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/component/eV/hjc/hjc$Fj;)Lcom/bytedance/sdk/component/eV/JU;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc$Fj;->ex:Lcom/bytedance/sdk/component/eV/JU;

    return-object p0
.end method

.method private Fj(Landroid/widget/ImageView;)Z
    .locals 2

    const/4 v0, 0x0

    if-eqz p1, :cond_0

    const v1, 0x413c0901

    invoke-virtual {p1, v1}, Landroid/view/View;->getTag(I)Ljava/lang/Object;

    move-result-object p1

    if-eqz p1, :cond_0

    iget-object v1, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc$Fj;->Fj:Lcom/bytedance/sdk/component/eV/hjc/hjc;

    invoke-static {v1}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->Ko(Lcom/bytedance/sdk/component/eV/hjc/hjc;)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p1, v1}, Ljava/lang/Object;->equals(Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_0

    const/4 p1, 0x1

    return p1

    :cond_0
    return v0
.end method


# virtual methods
.method public Fj(ILjava/lang/String;Ljava/lang/Throwable;)V
    .locals 2

    iget-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc$Fj;->Fj:Lcom/bytedance/sdk/component/eV/hjc/hjc;

    invoke-static {v0}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->mSE(Lcom/bytedance/sdk/component/eV/hjc/hjc;)I

    move-result v0

    const/4 v1, 0x5

    if-ne v0, v1, :cond_0

    iget-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc$Fj;->Fj:Lcom/bytedance/sdk/component/eV/hjc/hjc;

    invoke-static {v0}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->svN(Lcom/bytedance/sdk/component/eV/hjc/hjc;)Landroid/os/Handler;

    move-result-object v0

    new-instance v1, Lcom/bytedance/sdk/component/eV/hjc/hjc$Fj$3;

    invoke-direct {v1, p0, p1, p2, p3}, Lcom/bytedance/sdk/component/eV/hjc/hjc$Fj$3;-><init>(Lcom/bytedance/sdk/component/eV/hjc/hjc$Fj;ILjava/lang/String;Ljava/lang/Throwable;)V

    invoke-virtual {v0, v1}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    return-void

    :cond_0
    iget-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc$Fj;->ex:Lcom/bytedance/sdk/component/eV/JU;

    if-eqz v0, :cond_1

    invoke-interface {v0, p1, p2, p3}, Lcom/bytedance/sdk/component/eV/JU;->Fj(ILjava/lang/String;Ljava/lang/Throwable;)V

    :cond_1
    return-void
.end method

.method public Fj(Lcom/bytedance/sdk/component/eV/rAx;)V
    .locals 4

    iget-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc$Fj;->Fj:Lcom/bytedance/sdk/component/eV/hjc/hjc;

    invoke-static {v0}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->Ubf(Lcom/bytedance/sdk/component/eV/hjc/hjc;)Ljava/lang/ref/WeakReference;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/ref/Reference;->get()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroid/widget/ImageView;

    if-eqz v0, :cond_0

    iget-object v1, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc$Fj;->Fj:Lcom/bytedance/sdk/component/eV/hjc/hjc;

    invoke-static {v1}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->WR(Lcom/bytedance/sdk/component/eV/hjc/hjc;)I

    move-result v1

    const/4 v2, 0x3

    if-eq v1, v2, :cond_0

    invoke-direct {p0, v0}, Lcom/bytedance/sdk/component/eV/hjc/hjc$Fj;->Fj(Landroid/widget/ImageView;)Z

    move-result v1

    if-eqz v1, :cond_0

    invoke-interface {p1}, Lcom/bytedance/sdk/component/eV/rAx;->ex()Ljava/lang/Object;

    move-result-object v1

    instance-of v1, v1, Landroid/graphics/Bitmap;

    if-eqz v1, :cond_0

    invoke-interface {p1}, Lcom/bytedance/sdk/component/eV/rAx;->ex()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Landroid/graphics/Bitmap;

    iget-object v2, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc$Fj;->Fj:Lcom/bytedance/sdk/component/eV/hjc/hjc;

    invoke-static {v2}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->svN(Lcom/bytedance/sdk/component/eV/hjc/hjc;)Landroid/os/Handler;

    move-result-object v2

    new-instance v3, Lcom/bytedance/sdk/component/eV/hjc/hjc$Fj$1;

    invoke-direct {v3, p0, v0, v1}, Lcom/bytedance/sdk/component/eV/hjc/hjc$Fj$1;-><init>(Lcom/bytedance/sdk/component/eV/hjc/hjc$Fj;Landroid/widget/ImageView;Landroid/graphics/Bitmap;)V

    invoke-virtual {v2, v3}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    :cond_0
    :try_start_0
    iget-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc$Fj;->Fj:Lcom/bytedance/sdk/component/eV/hjc/hjc;

    invoke-static {v0}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->BcC(Lcom/bytedance/sdk/component/eV/hjc/hjc;)Lcom/bytedance/sdk/component/eV/BcC;

    move-result-object v0

    if-eqz v0, :cond_1

    invoke-interface {p1}, Lcom/bytedance/sdk/component/eV/rAx;->ex()Ljava/lang/Object;

    move-result-object v0

    instance-of v0, v0, Landroid/graphics/Bitmap;

    if-eqz v0, :cond_1

    iget-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc$Fj;->Fj:Lcom/bytedance/sdk/component/eV/hjc/hjc;

    invoke-static {v0}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->BcC(Lcom/bytedance/sdk/component/eV/hjc/hjc;)Lcom/bytedance/sdk/component/eV/BcC;

    move-result-object v0

    invoke-interface {p1}, Lcom/bytedance/sdk/component/eV/rAx;->ex()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Landroid/graphics/Bitmap;

    invoke-interface {v0, v1}, Lcom/bytedance/sdk/component/eV/BcC;->Fj(Landroid/graphics/Bitmap;)Landroid/graphics/Bitmap;

    move-result-object v0

    if-eqz v0, :cond_1

    invoke-interface {p1, v0}, Lcom/bytedance/sdk/component/eV/rAx;->Fj(Ljava/lang/Object;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    goto :goto_0

    :catchall_0
    nop

    :cond_1
    :goto_0
    iget-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc$Fj;->Fj:Lcom/bytedance/sdk/component/eV/hjc/hjc;

    invoke-static {v0}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->mSE(Lcom/bytedance/sdk/component/eV/hjc/hjc;)I

    move-result v0

    const/4 v1, 0x5

    if-ne v0, v1, :cond_2

    iget-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc$Fj;->Fj:Lcom/bytedance/sdk/component/eV/hjc/hjc;

    invoke-static {v0}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->svN(Lcom/bytedance/sdk/component/eV/hjc/hjc;)Landroid/os/Handler;

    move-result-object v0

    new-instance v1, Lcom/bytedance/sdk/component/eV/hjc/hjc$Fj$2;

    invoke-direct {v1, p0, p1}, Lcom/bytedance/sdk/component/eV/hjc/hjc$Fj$2;-><init>(Lcom/bytedance/sdk/component/eV/hjc/hjc$Fj;Lcom/bytedance/sdk/component/eV/rAx;)V

    invoke-virtual {v0, v1}, Landroid/os/Handler;->postAtFrontOfQueue(Ljava/lang/Runnable;)Z

    return-void

    :cond_2
    iget-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc$Fj;->ex:Lcom/bytedance/sdk/component/eV/JU;

    if-eqz v0, :cond_3

    invoke-interface {v0, p1}, Lcom/bytedance/sdk/component/eV/JU;->Fj(Lcom/bytedance/sdk/component/eV/rAx;)V

    :cond_3
    return-void
.end method
