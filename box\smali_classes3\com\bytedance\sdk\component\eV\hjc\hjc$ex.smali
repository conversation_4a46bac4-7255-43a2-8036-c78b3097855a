.class public Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/sdk/component/eV/Ko;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bytedance/sdk/component/eV/hjc/hjc;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "ex"
.end annotation


# instance fields
.field private Af:Ljava/util/concurrent/ExecutorService;

.field private BcC:I

.field private Fj:Lcom/bytedance/sdk/component/eV/JU;

.field private JU:Lcom/bytedance/sdk/component/eV/hjc/WR;

.field private JW:Lcom/bytedance/sdk/component/eV/ex;

.field private Ko:I

.field private Ql:Lcom/bytedance/sdk/component/eV/BcC;

.field private Tc:Ljava/lang/String;

.field private UYd:Z

.field private Ubf:Landroid/widget/ImageView$ScaleType;

.field private WR:Landroid/graphics/Bitmap$Config;

.field private cB:Lcom/bytedance/sdk/component/eV/Tc;

.field private dG:Z

.field private eV:Ljava/lang/String;

.field private ex:Landroid/widget/ImageView;

.field private hjc:Ljava/lang/String;

.field private mC:Z

.field private mE:Z

.field private mSE:I

.field private rAx:Lcom/bytedance/sdk/component/eV/mE;

.field private rS:I

.field private svN:I

.field private vYf:I


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/component/eV/hjc/WR;)V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, 0x1

    iput v0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;->mSE:I

    const/4 v0, 0x5

    iput v0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;->Ko:I

    iput-object p1, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;->JU:Lcom/bytedance/sdk/component/eV/hjc/WR;

    return-void
.end method

.method public static synthetic Af(Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;)Lcom/bytedance/sdk/component/eV/Tc;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;->cB:Lcom/bytedance/sdk/component/eV/Tc;

    return-object p0
.end method

.method public static synthetic BcC(Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;)I
    .locals 0

    iget p0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;->mSE:I

    return p0
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;)Ljava/lang/String;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;->eV:Ljava/lang/String;

    return-object p0
.end method

.method public static synthetic JU(Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;)I
    .locals 0

    iget p0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;->vYf:I

    return p0
.end method

.method public static synthetic JW(Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;)Lcom/bytedance/sdk/component/eV/BcC;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;->Ql:Lcom/bytedance/sdk/component/eV/BcC;

    return-object p0
.end method

.method public static synthetic Ko(Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;)Lcom/bytedance/sdk/component/eV/mE;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;->rAx:Lcom/bytedance/sdk/component/eV/mE;

    return-object p0
.end method

.method public static synthetic Ql(Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;)I
    .locals 0

    iget p0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;->rS:I

    return p0
.end method

.method public static synthetic Tc(Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;)Lcom/bytedance/sdk/component/eV/hjc/WR;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;->JU:Lcom/bytedance/sdk/component/eV/hjc/WR;

    return-object p0
.end method

.method public static synthetic UYd(Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;)Z
    .locals 0

    iget-boolean p0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;->UYd:Z

    return p0
.end method

.method public static synthetic Ubf(Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;)Landroid/graphics/Bitmap$Config;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;->WR:Landroid/graphics/Bitmap$Config;

    return-object p0
.end method

.method public static synthetic WR(Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;)I
    .locals 0

    iget p0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;->svN:I

    return p0
.end method

.method public static synthetic cB(Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;)Ljava/lang/String;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;->Tc:Ljava/lang/String;

    return-object p0
.end method

.method public static synthetic dG(Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;)Z
    .locals 0

    iget-boolean p0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;->dG:Z

    return p0
.end method

.method public static synthetic eV(Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;)Landroid/widget/ImageView$ScaleType;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;->Ubf:Landroid/widget/ImageView$ScaleType;

    return-object p0
.end method

.method public static synthetic ex(Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;)Lcom/bytedance/sdk/component/eV/JU;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;->Fj:Lcom/bytedance/sdk/component/eV/JU;

    return-object p0
.end method

.method public static synthetic hjc(Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;)Landroid/widget/ImageView;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;->ex:Landroid/widget/ImageView;

    return-object p0
.end method

.method public static synthetic mC(Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;)Lcom/bytedance/sdk/component/eV/ex;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;->JW:Lcom/bytedance/sdk/component/eV/ex;

    return-object p0
.end method

.method public static synthetic mE(Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;)Z
    .locals 0

    iget-boolean p0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;->mC:Z

    return p0
.end method

.method public static synthetic mSE(Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;)I
    .locals 0

    iget p0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;->Ko:I

    return p0
.end method

.method public static synthetic rAx(Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;)Ljava/lang/String;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;->hjc:Ljava/lang/String;

    return-object p0
.end method

.method public static synthetic rS(Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;)Ljava/util/concurrent/ExecutorService;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;->Af:Ljava/util/concurrent/ExecutorService;

    return-object p0
.end method

.method public static synthetic svN(Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;)I
    .locals 0

    iget p0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;->BcC:I

    return p0
.end method

.method public static synthetic vYf(Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;)Z
    .locals 0

    iget-boolean p0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;->mE:Z

    return p0
.end method


# virtual methods
.method public Fj(I)Lcom/bytedance/sdk/component/eV/Ko;
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;->svN:I

    return-object p0
.end method

.method public Fj(Landroid/graphics/Bitmap$Config;)Lcom/bytedance/sdk/component/eV/Ko;
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;->WR:Landroid/graphics/Bitmap$Config;

    return-object p0
.end method

.method public Fj(Landroid/widget/ImageView$ScaleType;)Lcom/bytedance/sdk/component/eV/Ko;
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;->Ubf:Landroid/widget/ImageView$ScaleType;

    return-object p0
.end method

.method public Fj(Lcom/bytedance/sdk/component/eV/BcC;)Lcom/bytedance/sdk/component/eV/Ko;
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;->Ql:Lcom/bytedance/sdk/component/eV/BcC;

    return-object p0
.end method

.method public Fj(Lcom/bytedance/sdk/component/eV/mE;)Lcom/bytedance/sdk/component/eV/Ko;
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;->rAx:Lcom/bytedance/sdk/component/eV/mE;

    return-object p0
.end method

.method public Fj(Ljava/lang/String;)Lcom/bytedance/sdk/component/eV/Ko;
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;->hjc:Ljava/lang/String;

    return-object p0
.end method

.method public Fj(Z)Lcom/bytedance/sdk/component/eV/Ko;
    .locals 0

    iput-boolean p1, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;->dG:Z

    return-object p0
.end method

.method public Fj(Landroid/widget/ImageView;)Lcom/bytedance/sdk/component/eV/mSE;
    .locals 1

    iput-object p1, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;->ex:Landroid/widget/ImageView;

    new-instance p1, Lcom/bytedance/sdk/component/eV/hjc/hjc;

    const/4 v0, 0x0

    invoke-direct {p1, p0, v0}, Lcom/bytedance/sdk/component/eV/hjc/hjc;-><init>(Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;Lcom/bytedance/sdk/component/eV/hjc/hjc$1;)V

    invoke-static {p1}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->eV(Lcom/bytedance/sdk/component/eV/hjc/hjc;)Lcom/bytedance/sdk/component/eV/mSE;

    move-result-object p1

    return-object p1
.end method

.method public Fj(Lcom/bytedance/sdk/component/eV/JU;)Lcom/bytedance/sdk/component/eV/mSE;
    .locals 1

    iput-object p1, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;->Fj:Lcom/bytedance/sdk/component/eV/JU;

    new-instance p1, Lcom/bytedance/sdk/component/eV/hjc/hjc;

    const/4 v0, 0x0

    invoke-direct {p1, p0, v0}, Lcom/bytedance/sdk/component/eV/hjc/hjc;-><init>(Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;Lcom/bytedance/sdk/component/eV/hjc/hjc$1;)V

    invoke-static {p1}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->eV(Lcom/bytedance/sdk/component/eV/hjc/hjc;)Lcom/bytedance/sdk/component/eV/mSE;

    move-result-object p1

    return-object p1
.end method

.method public Fj(Lcom/bytedance/sdk/component/eV/JU;I)Lcom/bytedance/sdk/component/eV/mSE;
    .locals 0

    iput p2, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;->Ko:I

    invoke-virtual {p0, p1}, Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;->Fj(Lcom/bytedance/sdk/component/eV/JU;)Lcom/bytedance/sdk/component/eV/mSE;

    move-result-object p1

    return-object p1
.end method

.method public Ubf(I)Lcom/bytedance/sdk/component/eV/Ko;
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;->vYf:I

    return-object p0
.end method

.method public eV(I)Lcom/bytedance/sdk/component/eV/Ko;
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;->rS:I

    return-object p0
.end method

.method public ex(I)Lcom/bytedance/sdk/component/eV/Ko;
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;->BcC:I

    return-object p0
.end method

.method public ex(Ljava/lang/String;)Lcom/bytedance/sdk/component/eV/Ko;
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;->Tc:Ljava/lang/String;

    return-object p0
.end method

.method public hjc(I)Lcom/bytedance/sdk/component/eV/Ko;
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;->mSE:I

    return-object p0
.end method

.method public hjc(Ljava/lang/String;)Lcom/bytedance/sdk/component/eV/Ko;
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;->eV:Ljava/lang/String;

    return-object p0
.end method
