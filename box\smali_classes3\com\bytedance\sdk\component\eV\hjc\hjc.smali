.class public Lcom/bytedance/sdk/component/eV/hjc/hjc;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/sdk/component/eV/mSE;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/bytedance/sdk/component/eV/hjc/hjc$Fj;,
        Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;
    }
.end annotation


# instance fields
.field private Af:Lcom/bytedance/sdk/component/eV/svN;

.field private BcC:I

.field Fj:Ljava/util/concurrent/Future;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/concurrent/Future<",
            "*>;"
        }
    .end annotation
.end field

.field private JU:Lcom/bytedance/sdk/component/eV/mE;

.field private JW:Z

.field private Ko:Lcom/bytedance/sdk/component/eV/BcC;

.field private Moo:I

.field private Ql:I

.field private Tc:Z

.field private UYd:Ljava/lang/ref/WeakReference;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/lang/ref/WeakReference<",
            "Landroid/widget/ImageView;",
            ">;"
        }
    .end annotation
.end field

.field private Ubf:Lcom/bytedance/sdk/component/eV/JU;

.field private Vq:Lcom/bytedance/sdk/component/eV/ex;

.field private WR:Landroid/widget/ImageView$ScaleType;

.field private cB:Lcom/bytedance/sdk/component/eV/hjc/WR;

.field private volatile dG:Z

.field private eV:Ljava/lang/String;

.field private ex:Ljava/lang/String;

.field private fj:Lcom/bytedance/sdk/component/eV/Tc;

.field private hjc:Ljava/lang/String;

.field private lv:Ljava/util/concurrent/ExecutorService;

.field private mC:I

.field private mE:Z

.field private mSE:I

.field private nsB:Lcom/bytedance/sdk/component/eV/hjc/Fj;

.field private rAx:I

.field private rS:Ljava/util/Queue;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Queue<",
            "Lcom/bytedance/sdk/component/eV/eV/mSE;",
            ">;"
        }
    .end annotation
.end field

.field private rXP:Z

.field private rf:I

.field private svN:Landroid/graphics/Bitmap$Config;

.field private uy:Z

.field private final vYf:Landroid/os/Handler;


# direct methods
.method private constructor <init>(Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;)V
    .locals 2

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    new-instance v0, Ljava/util/concurrent/LinkedBlockingQueue;

    invoke-direct {v0}, Ljava/util/concurrent/LinkedBlockingQueue;-><init>()V

    iput-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc;->rS:Ljava/util/Queue;

    new-instance v0, Landroid/os/Handler;

    invoke-static {}, Landroid/os/Looper;->getMainLooper()Landroid/os/Looper;

    move-result-object v1

    invoke-direct {v0, v1}, Landroid/os/Handler;-><init>(Landroid/os/Looper;)V

    iput-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc;->vYf:Landroid/os/Handler;

    const/4 v0, 0x1

    iput-boolean v0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc;->mE:Z

    invoke-static {p1}, Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;->Fj(Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;)Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc;->ex:Ljava/lang/String;

    new-instance v0, Lcom/bytedance/sdk/component/eV/hjc/hjc$Fj;

    invoke-static {p1}, Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;->ex(Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;)Lcom/bytedance/sdk/component/eV/JU;

    move-result-object v1

    invoke-direct {v0, p0, v1}, Lcom/bytedance/sdk/component/eV/hjc/hjc$Fj;-><init>(Lcom/bytedance/sdk/component/eV/hjc/hjc;Lcom/bytedance/sdk/component/eV/JU;)V

    iput-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc;->Ubf:Lcom/bytedance/sdk/component/eV/JU;

    new-instance v0, Ljava/lang/ref/WeakReference;

    invoke-static {p1}, Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;->hjc(Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;)Landroid/widget/ImageView;

    move-result-object v1

    invoke-direct {v0, v1}, Ljava/lang/ref/WeakReference;-><init>(Ljava/lang/Object;)V

    iput-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc;->UYd:Ljava/lang/ref/WeakReference;

    invoke-static {p1}, Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;->eV(Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;)Landroid/widget/ImageView$ScaleType;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc;->WR:Landroid/widget/ImageView$ScaleType;

    invoke-static {p1}, Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;->Ubf(Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;)Landroid/graphics/Bitmap$Config;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc;->svN:Landroid/graphics/Bitmap$Config;

    invoke-static {p1}, Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;->WR(Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;)I

    move-result v0

    iput v0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc;->BcC:I

    invoke-static {p1}, Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;->svN(Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;)I

    move-result v0

    iput v0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc;->mSE:I

    invoke-static {p1}, Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;->BcC(Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;)I

    move-result v0

    iput v0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc;->rAx:I

    invoke-static {p1}, Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;->mSE(Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;)I

    move-result v0

    iput v0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc;->Ql:I

    invoke-static {p1}, Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;->Ko(Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;)Lcom/bytedance/sdk/component/eV/mE;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc;->JU:Lcom/bytedance/sdk/component/eV/mE;

    invoke-direct {p0, p1}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->Fj(Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;)Lcom/bytedance/sdk/component/eV/ex;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc;->Vq:Lcom/bytedance/sdk/component/eV/ex;

    invoke-static {p1}, Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;->rAx(Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;)Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-nez v0, :cond_0

    invoke-static {p1}, Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;->rAx(Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0, v0}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->ex(Ljava/lang/String;)V

    invoke-static {p1}, Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;->rAx(Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0, v0}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->Fj(Ljava/lang/String;)V

    :cond_0
    invoke-static {p1}, Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;->UYd(Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;)Z

    move-result v0

    iput-boolean v0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc;->Tc:Z

    invoke-static {p1}, Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;->dG(Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;)Z

    move-result v0

    iput-boolean v0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc;->JW:Z

    invoke-static {p1}, Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;->Tc(Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;)Lcom/bytedance/sdk/component/eV/hjc/WR;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc;->cB:Lcom/bytedance/sdk/component/eV/hjc/WR;

    invoke-static {p1}, Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;->JW(Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;)Lcom/bytedance/sdk/component/eV/BcC;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc;->Ko:Lcom/bytedance/sdk/component/eV/BcC;

    invoke-static {p1}, Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;->JU(Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;)I

    move-result v0

    iput v0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc;->rf:I

    invoke-static {p1}, Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;->Ql(Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;)I

    move-result v0

    iput v0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc;->Moo:I

    invoke-static {p1}, Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;->rS(Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;)Ljava/util/concurrent/ExecutorService;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc;->lv:Ljava/util/concurrent/ExecutorService;

    invoke-static {p1}, Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;->vYf(Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;)Z

    move-result v0

    iput-boolean v0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc;->uy:Z

    invoke-static {p1}, Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;->mE(Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;)Z

    move-result v0

    iput-boolean v0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc;->rXP:Z

    invoke-static {p1}, Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;->Af(Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;)Lcom/bytedance/sdk/component/eV/Tc;

    move-result-object p1

    iput-object p1, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc;->fj:Lcom/bytedance/sdk/component/eV/Tc;

    iget-object p1, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc;->rS:Ljava/util/Queue;

    new-instance v0, Lcom/bytedance/sdk/component/eV/eV/hjc;

    invoke-direct {v0}, Lcom/bytedance/sdk/component/eV/eV/hjc;-><init>()V

    invoke-interface {p1, v0}, Ljava/util/Queue;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public synthetic constructor <init>(Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;Lcom/bytedance/sdk/component/eV/hjc/hjc$1;)V
    .locals 0

    invoke-direct {p0, p1}, Lcom/bytedance/sdk/component/eV/hjc/hjc;-><init>(Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;)V

    return-void
.end method

.method public static synthetic BcC(Lcom/bytedance/sdk/component/eV/hjc/hjc;)Lcom/bytedance/sdk/component/eV/BcC;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc;->Ko:Lcom/bytedance/sdk/component/eV/BcC;

    return-object p0
.end method

.method private Fj(Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;)Lcom/bytedance/sdk/component/eV/ex;
    .locals 1

    invoke-static {p1}, Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;->mC(Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;)Lcom/bytedance/sdk/component/eV/ex;

    move-result-object v0

    if-eqz v0, :cond_0

    invoke-static {p1}, Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;->mC(Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;)Lcom/bytedance/sdk/component/eV/ex;

    move-result-object p1

    return-object p1

    :cond_0
    invoke-static {p1}, Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;->cB(Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;)Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-nez v0, :cond_1

    new-instance v0, Ljava/io/File;

    invoke-static {p1}, Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;->cB(Lcom/bytedance/sdk/component/eV/hjc/hjc$ex;)Ljava/lang/String;

    move-result-object p1

    invoke-direct {v0, p1}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    invoke-static {v0}, Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj;->Fj(Ljava/io/File;)Lcom/bytedance/sdk/component/eV/ex;

    move-result-object p1

    return-object p1

    :cond_1
    invoke-static {}, Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj;->svN()Lcom/bytedance/sdk/component/eV/ex;

    move-result-object p1

    return-object p1
.end method

.method private Fj(ILjava/lang/String;Ljava/lang/Throwable;)V
    .locals 1

    new-instance v0, Lcom/bytedance/sdk/component/eV/eV/BcC;

    invoke-direct {v0, p1, p2, p3}, Lcom/bytedance/sdk/component/eV/eV/BcC;-><init>(ILjava/lang/String;Ljava/lang/Throwable;)V

    invoke-virtual {v0, p0}, Lcom/bytedance/sdk/component/eV/eV/BcC;->Fj(Lcom/bytedance/sdk/component/eV/hjc/hjc;)V

    iget-object p1, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc;->rS:Ljava/util/Queue;

    invoke-interface {p1}, Ljava/util/Collection;->clear()V

    return-void
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/component/eV/hjc/hjc;ILjava/lang/String;Ljava/lang/Throwable;)V
    .locals 0

    invoke-direct {p0, p1, p2, p3}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->Fj(ILjava/lang/String;Ljava/lang/Throwable;)V

    return-void
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/component/eV/hjc/hjc;)Z
    .locals 0

    iget-boolean p0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc;->dG:Z

    return p0
.end method

.method public static synthetic Ko(Lcom/bytedance/sdk/component/eV/hjc/hjc;)Ljava/lang/String;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc;->hjc:Ljava/lang/String;

    return-object p0
.end method

.method public static synthetic Ubf(Lcom/bytedance/sdk/component/eV/hjc/hjc;)Ljava/lang/ref/WeakReference;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc;->UYd:Ljava/lang/ref/WeakReference;

    return-object p0
.end method

.method public static synthetic WR(Lcom/bytedance/sdk/component/eV/hjc/hjc;)I
    .locals 0

    iget p0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc;->rAx:I

    return p0
.end method

.method public static synthetic eV(Lcom/bytedance/sdk/component/eV/hjc/hjc;)Lcom/bytedance/sdk/component/eV/mSE;
    .locals 0

    invoke-direct {p0}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->nsB()Lcom/bytedance/sdk/component/eV/mSE;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic ex(Lcom/bytedance/sdk/component/eV/hjc/hjc;)Ljava/util/Queue;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc;->rS:Ljava/util/Queue;

    return-object p0
.end method

.method public static synthetic hjc(Lcom/bytedance/sdk/component/eV/hjc/hjc;)Lcom/bytedance/sdk/component/eV/mE;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc;->JU:Lcom/bytedance/sdk/component/eV/mE;

    return-object p0
.end method

.method public static synthetic mSE(Lcom/bytedance/sdk/component/eV/hjc/hjc;)I
    .locals 0

    iget p0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc;->Ql:I

    return p0
.end method

.method private nsB()Lcom/bytedance/sdk/component/eV/mSE;
    .locals 4

    :try_start_0
    iget-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc;->cB:Lcom/bytedance/sdk/component/eV/hjc/WR;

    if-nez v0, :cond_1

    iget-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc;->Ubf:Lcom/bytedance/sdk/component/eV/JU;

    if-eqz v0, :cond_0

    const-string v1, "not init !"

    const/4 v2, 0x0

    const/16 v3, 0x3ed

    invoke-interface {v0, v3, v1, v2}, Lcom/bytedance/sdk/component/eV/JU;->Fj(ILjava/lang/String;Ljava/lang/Throwable;)V

    goto :goto_0

    :catch_0
    move-exception v0

    goto :goto_1

    :cond_0
    :goto_0
    return-object p0

    :cond_1
    iget-object v1, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc;->lv:Ljava/util/concurrent/ExecutorService;

    if-nez v1, :cond_2

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/eV/hjc/WR;->WR()Ljava/util/concurrent/ExecutorService;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc;->lv:Ljava/util/concurrent/ExecutorService;

    :cond_2
    new-instance v0, Lcom/bytedance/sdk/component/eV/hjc/hjc$1;

    invoke-direct {v0, p0}, Lcom/bytedance/sdk/component/eV/hjc/hjc$1;-><init>(Lcom/bytedance/sdk/component/eV/hjc/hjc;)V

    iget-boolean v1, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc;->rXP:Z

    if-eqz v1, :cond_3

    invoke-interface {v0}, Ljava/lang/Runnable;->run()V

    goto :goto_2

    :cond_3
    iget-object v1, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc;->lv:Ljava/util/concurrent/ExecutorService;

    if-eqz v1, :cond_4

    invoke-interface {v1, v0}, Ljava/util/concurrent/ExecutorService;->submit(Ljava/lang/Runnable;)Ljava/util/concurrent/Future;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc;->Fj:Ljava/util/concurrent/Future;
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_2

    :goto_1
    const-string v1, "ImageRequest"

    invoke-virtual {v0}, Ljava/lang/Throwable;->getMessage()Ljava/lang/String;

    move-result-object v0

    invoke-static {v1, v0}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    :cond_4
    :goto_2
    return-object p0
.end method

.method public static synthetic svN(Lcom/bytedance/sdk/component/eV/hjc/hjc;)Landroid/os/Handler;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc;->vYf:Landroid/os/Handler;

    return-object p0
.end method


# virtual methods
.method public Af()Z
    .locals 1

    iget-boolean v0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc;->uy:Z

    return v0
.end method

.method public BcC()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc;->rf:I

    return v0
.end method

.method public Fj()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc;->ex:Ljava/lang/String;

    return-object v0
.end method

.method public Fj(I)V
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc;->mC:I

    return-void
.end method

.method public Fj(Lcom/bytedance/sdk/component/eV/hjc/Fj;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc;->nsB:Lcom/bytedance/sdk/component/eV/hjc/Fj;

    return-void
.end method

.method public Fj(Lcom/bytedance/sdk/component/eV/svN;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc;->Af:Lcom/bytedance/sdk/component/eV/svN;

    return-void
.end method

.method public Fj(Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc;->eV:Ljava/lang/String;

    return-void
.end method

.method public Fj(Z)V
    .locals 0

    iput-boolean p1, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc;->mE:Z

    return-void
.end method

.method public Fj(Lcom/bytedance/sdk/component/eV/eV/mSE;)Z
    .locals 1

    iget-boolean v0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc;->dG:Z

    if-eqz v0, :cond_0

    const/4 p1, 0x0

    return p1

    :cond_0
    iget-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc;->rS:Ljava/util/Queue;

    invoke-interface {v0, p1}, Ljava/util/Queue;->add(Ljava/lang/Object;)Z

    move-result p1

    return p1
.end method

.method public JU()Lcom/bytedance/sdk/component/eV/svN;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc;->Af:Lcom/bytedance/sdk/component/eV/svN;

    return-object v0
.end method

.method public JW()Z
    .locals 1

    iget-boolean v0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc;->mE:Z

    return v0
.end method

.method public Ko()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc;->eV:Ljava/lang/String;

    return-object v0
.end method

.method public Ql()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc;->mC:I

    return v0
.end method

.method public Tc()Z
    .locals 1

    iget-boolean v0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc;->JW:Z

    return v0
.end method

.method public UYd()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc;->rAx:I

    return v0
.end method

.method public Ubf()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc;->hjc:Ljava/lang/String;

    return-object v0
.end method

.method public WR()Landroid/graphics/Bitmap$Config;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc;->svN:Landroid/graphics/Bitmap$Config;

    return-object v0
.end method

.method public cB()Ljava/lang/String;
    .locals 2

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->Ubf()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->UYd()I

    move-result v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public dG()Z
    .locals 1

    iget-boolean v0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc;->Tc:Z

    return v0
.end method

.method public eV()Landroid/widget/ImageView$ScaleType;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc;->WR:Landroid/widget/ImageView$ScaleType;

    return-object v0
.end method

.method public ex()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc;->BcC:I

    return v0
.end method

.method public ex(Ljava/lang/String;)V
    .locals 2

    iget-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc;->UYd:Ljava/lang/ref/WeakReference;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Ljava/lang/ref/Reference;->get()Ljava/lang/Object;

    move-result-object v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc;->UYd:Ljava/lang/ref/WeakReference;

    invoke-virtual {v0}, Ljava/lang/ref/Reference;->get()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroid/widget/ImageView;

    const v1, 0x413c0901

    invoke-virtual {v0, v1, p1}, Landroid/view/View;->setTag(ILjava/lang/Object;)V

    :cond_0
    iput-object p1, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc;->hjc:Ljava/lang/String;

    return-void
.end method

.method public hjc()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc;->mSE:I

    return v0
.end method

.method public mC()Lcom/bytedance/sdk/component/eV/Tc;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc;->fj:Lcom/bytedance/sdk/component/eV/Tc;

    return-object v0
.end method

.method public mE()Lcom/bytedance/sdk/component/eV/ex;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc;->Vq:Lcom/bytedance/sdk/component/eV/ex;

    return-object v0
.end method

.method public mSE()Lcom/bytedance/sdk/component/eV/JU;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc;->Ubf:Lcom/bytedance/sdk/component/eV/JU;

    return-object v0
.end method

.method public rAx()Landroid/graphics/Bitmap$Config;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc;->svN:Landroid/graphics/Bitmap$Config;

    return-object v0
.end method

.method public rS()Lcom/bytedance/sdk/component/eV/hjc/Fj;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc;->nsB:Lcom/bytedance/sdk/component/eV/hjc/Fj;

    return-object v0
.end method

.method public svN()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc;->Moo:I

    return v0
.end method

.method public vYf()Lcom/bytedance/sdk/component/eV/hjc/WR;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/hjc;->cB:Lcom/bytedance/sdk/component/eV/hjc/WR;

    return-object v0
.end method
