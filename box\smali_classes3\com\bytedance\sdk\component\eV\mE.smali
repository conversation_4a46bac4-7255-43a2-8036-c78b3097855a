.class public interface abstract Lcom/bytedance/sdk/component/eV/mE;
.super Ljava/lang/Object;


# annotations
.annotation runtime Lcom/bytedance/sdk/openadsdk/ats/ATSApi;
.end annotation


# virtual methods
.method public abstract Fj(Ljava/lang/String;Lcom/bytedance/sdk/component/eV/mSE;)V
    .annotation runtime Lcom/bytedance/sdk/openadsdk/ats/ATSMethod;
        value = 0x1
    .end annotation
.end method

.method public abstract ex(Ljava/lang/String;Lcom/bytedance/sdk/component/eV/mSE;)V
    .annotation runtime Lcom/bytedance/sdk/openadsdk/ats/ATSMethod;
        value = 0x2
    .end annotation
.end method
