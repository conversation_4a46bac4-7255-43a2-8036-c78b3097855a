.class public interface abstract Lcom/bytedance/sdk/component/eV/mSE;
.super Ljava/lang/Object;


# annotations
.annotation runtime Lcom/bytedance/sdk/openadsdk/ats/ATSApi;
.end annotation


# virtual methods
.method public abstract Fj()Ljava/lang/String;
    .annotation runtime Lcom/bytedance/sdk/openadsdk/ats/ATSMethod;
        value = 0x2
    .end annotation
.end method

.method public abstract Ubf()Ljava/lang/String;
    .annotation runtime Lcom/bytedance/sdk/openadsdk/ats/ATSMethod;
        value = 0x7
    .end annotation
.end method

.method public abstract WR()Landroid/graphics/Bitmap$Config;
    .annotation runtime Lcom/bytedance/sdk/openadsdk/ats/ATSMethod;
        value = 0x8
    .end annotation
.end method

.method public abstract eV()Landroid/widget/ImageView$ScaleType;
    .annotation runtime Lcom/bytedance/sdk/openadsdk/ats/ATSMethod;
        value = 0x5
    .end annotation
.end method

.method public abstract ex()I
    .annotation runtime Lcom/bytedance/sdk/openadsdk/ats/ATSMethod;
        value = 0x3
    .end annotation
.end method

.method public abstract hjc()I
    .annotation runtime Lcom/bytedance/sdk/openadsdk/ats/ATSMethod;
        value = 0x4
    .end annotation
.end method
