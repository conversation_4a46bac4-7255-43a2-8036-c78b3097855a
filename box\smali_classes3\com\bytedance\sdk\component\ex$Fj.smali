.class public Lcom/bytedance/sdk/component/ex$Fj;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bytedance/sdk/component/ex;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "Fj"
.end annotation


# direct methods
.method public static Fj(Ljava/util/concurrent/ExecutorService;)V
    .locals 0

    invoke-static {p0}, Lcom/bytedance/sdk/component/ex;->Fj(Ljava/util/concurrent/ExecutorService;)Ljava/util/concurrent/ExecutorService;

    return-void
.end method
