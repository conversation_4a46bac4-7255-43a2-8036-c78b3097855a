.class Lcom/bytedance/sdk/component/ex$ex;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bytedance/sdk/component/ex;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "ex"
.end annotation


# instance fields
.field final Fj:J

.field Ubf:Z

.field volatile eV:Z

.field final ex:Ljava/util/Properties;

.field final hjc:Ljava/util/concurrent/CountDownLatch;


# direct methods
.method private constructor <init>(JLjava/util/Properties;)V
    .locals 2

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    new-instance v0, Ljava/util/concurrent/CountDownLatch;

    const/4 v1, 0x1

    invoke-direct {v0, v1}, Ljava/util/concurrent/CountDownLatch;-><init>(I)V

    iput-object v0, p0, Lcom/bytedance/sdk/component/ex$ex;->hjc:Ljava/util/concurrent/CountDownLatch;

    const/4 v0, 0x0

    iput-boolean v0, p0, Lcom/bytedance/sdk/component/ex$ex;->eV:Z

    iput-boolean v0, p0, Lcom/bytedance/sdk/component/ex$ex;->Ubf:Z

    iput-wide p1, p0, Lcom/bytedance/sdk/component/ex$ex;->Fj:J

    iput-object p3, p0, Lcom/bytedance/sdk/component/ex$ex;->ex:Ljava/util/Properties;

    return-void
.end method

.method public synthetic constructor <init>(JLjava/util/Properties;Lcom/bytedance/sdk/component/ex$1;)V
    .locals 0

    invoke-direct {p0, p1, p2, p3}, Lcom/bytedance/sdk/component/ex$ex;-><init>(JLjava/util/Properties;)V

    return-void
.end method


# virtual methods
.method public Fj(ZZ)V
    .locals 0

    iput-boolean p1, p0, Lcom/bytedance/sdk/component/ex$ex;->Ubf:Z

    iput-boolean p2, p0, Lcom/bytedance/sdk/component/ex$ex;->eV:Z

    iget-object p1, p0, Lcom/bytedance/sdk/component/ex$ex;->hjc:Ljava/util/concurrent/CountDownLatch;

    invoke-virtual {p1}, Ljava/util/concurrent/CountDownLatch;->countDown()V

    return-void
.end method
