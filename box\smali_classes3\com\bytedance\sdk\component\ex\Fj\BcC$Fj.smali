.class public interface abstract Lcom/bytedance/sdk/component/ex/Fj/BcC$Fj;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bytedance/sdk/component/ex/Fj/BcC;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "Fj"
.end annotation


# virtual methods
.method public abstract Fj(Lcom/bytedance/sdk/component/ex/Fj/dG;)Lcom/bytedance/sdk/component/ex/Fj/JW;
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation
.end method

.method public abstract Fj()Lcom/bytedance/sdk/component/ex/Fj/dG;
.end method
