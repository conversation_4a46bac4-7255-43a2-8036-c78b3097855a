.class public interface abstract Lcom/bytedance/sdk/component/ex/Fj/BcC;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/bytedance/sdk/component/ex/Fj/BcC$Fj;
    }
.end annotation


# virtual methods
.method public abstract Fj(Lcom/bytedance/sdk/component/ex/Fj/BcC$Fj;)Lcom/bytedance/sdk/component/ex/Fj/JW;
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation
.end method
