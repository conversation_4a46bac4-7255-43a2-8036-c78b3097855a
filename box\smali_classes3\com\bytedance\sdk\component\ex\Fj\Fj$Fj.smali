.class public final Lcom/bytedance/sdk/component/ex/Fj/Fj$Fj;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bytedance/sdk/component/ex/Fj/Fj;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "Fj"
.end annotation


# instance fields
.field Fj:Z


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public Fj()Lcom/bytedance/sdk/component/ex/Fj/Fj$Fj;
    .locals 1

    const/4 v0, 0x1

    iput-boolean v0, p0, Lcom/bytedance/sdk/component/ex/Fj/Fj$Fj;->Fj:Z

    return-object p0
.end method

.method public ex()Lcom/bytedance/sdk/component/ex/Fj/Fj;
    .locals 1

    new-instance v0, Lcom/bytedance/sdk/component/ex/Fj/Fj;

    invoke-direct {v0, p0}, Lcom/bytedance/sdk/component/ex/Fj/Fj;-><init>(Lcom/bytedance/sdk/component/ex/Fj/Fj$Fj;)V

    return-object v0
.end method
