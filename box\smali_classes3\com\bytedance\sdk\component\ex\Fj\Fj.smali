.class public final Lcom/bytedance/sdk/component/ex/Fj/Fj;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/bytedance/sdk/component/ex/Fj/Fj$Fj;
    }
.end annotation


# instance fields
.field public final Fj:Z


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/component/ex/Fj/Fj$Fj;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iget-boolean p1, p1, Lcom/bytedance/sdk/component/ex/Fj/Fj$Fj;->Fj:Z

    iput-boolean p1, p0, Lcom/bytedance/sdk/component/ex/Fj/Fj;->Fj:Z

    return-void
.end method
