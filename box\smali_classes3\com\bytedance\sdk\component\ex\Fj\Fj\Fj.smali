.class public Lcom/bytedance/sdk/component/ex/Fj/Fj/Fj;
.super Ljava/lang/Object;


# direct methods
.method public static Fj(Lcom/bytedance/sdk/component/ex/Fj/rAx$Fj;)Lcom/bytedance/sdk/component/ex/Fj/rAx;
    .locals 1

    new-instance v0, Lcom/bytedance/sdk/component/ex/Fj/Fj/Fj/hjc;

    invoke-direct {v0, p0}, Lcom/bytedance/sdk/component/ex/Fj/Fj/Fj/hjc;-><init>(Lcom/bytedance/sdk/component/ex/Fj/rAx$Fj;)V

    return-object v0
.end method
