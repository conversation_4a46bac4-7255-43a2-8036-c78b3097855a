.class Lcom/bytedance/sdk/component/ex/Fj/Fj/Fj/Fj$1;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/sdk/component/ex/Fj/BcC;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/component/ex/Fj/Fj/Fj/Fj;->Fj()Lcom/bytedance/sdk/component/ex/Fj/JW;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/component/ex/Fj/Fj/Fj/Fj;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/component/ex/Fj/Fj/Fj/Fj;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/ex/Fj/Fj/Fj/Fj$1;->Fj:Lcom/bytedance/sdk/component/ex/Fj/Fj/Fj/Fj;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public Fj(Lcom/bytedance/sdk/component/ex/Fj/BcC$Fj;)Lcom/bytedance/sdk/component/ex/Fj/JW;
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-object v0, p0, Lcom/bytedance/sdk/component/ex/Fj/Fj/Fj/Fj$1;->Fj:Lcom/bytedance/sdk/component/ex/Fj/Fj/Fj/Fj;

    invoke-interface {p1}, Lcom/bytedance/sdk/component/ex/Fj/BcC$Fj;->Fj()Lcom/bytedance/sdk/component/ex/Fj/dG;

    move-result-object p1

    invoke-virtual {v0, p1}, Lcom/bytedance/sdk/component/ex/Fj/Fj/Fj/Fj;->Fj(Lcom/bytedance/sdk/component/ex/Fj/dG;)Lcom/bytedance/sdk/component/ex/Fj/JW;

    move-result-object p1

    return-object p1
.end method
