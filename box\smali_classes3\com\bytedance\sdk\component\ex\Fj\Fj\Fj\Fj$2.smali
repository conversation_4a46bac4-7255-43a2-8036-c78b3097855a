.class Lcom/bytedance/sdk/component/ex/Fj/Fj/Fj/Fj$2;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/component/ex/Fj/Fj/Fj/Fj;->Fj(Lcom/bytedance/sdk/component/ex/Fj/hjc;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/component/ex/Fj/hjc;

.field final synthetic ex:Lcom/bytedance/sdk/component/ex/Fj/Fj/Fj/Fj;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/component/ex/Fj/Fj/Fj/Fj;Lcom/bytedance/sdk/component/ex/Fj/hjc;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/ex/Fj/Fj/Fj/Fj$2;->ex:Lcom/bytedance/sdk/component/ex/Fj/Fj/Fj/Fj;

    iput-object p2, p0, Lcom/bytedance/sdk/component/ex/Fj/Fj/Fj/Fj$2;->Fj:Lcom/bytedance/sdk/component/ex/Fj/hjc;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 4

    :try_start_0
    iget-object v0, p0, Lcom/bytedance/sdk/component/ex/Fj/Fj/Fj/Fj$2;->ex:Lcom/bytedance/sdk/component/ex/Fj/Fj/Fj/Fj;

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/ex/Fj/Fj/Fj/Fj;->Fj()Lcom/bytedance/sdk/component/ex/Fj/JW;

    move-result-object v0

    if-nez v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/sdk/component/ex/Fj/Fj/Fj/Fj$2;->Fj:Lcom/bytedance/sdk/component/ex/Fj/hjc;

    iget-object v1, p0, Lcom/bytedance/sdk/component/ex/Fj/Fj/Fj/Fj$2;->ex:Lcom/bytedance/sdk/component/ex/Fj/Fj/Fj/Fj;

    new-instance v2, Ljava/io/IOException;

    const-string v3, "response is null"

    invoke-direct {v2, v3}, Ljava/io/IOException;-><init>(Ljava/lang/String;)V

    invoke-interface {v0, v1, v2}, Lcom/bytedance/sdk/component/ex/Fj/hjc;->Fj(Lcom/bytedance/sdk/component/ex/Fj/ex;Ljava/io/IOException;)V

    return-void

    :catch_0
    move-exception v0

    goto :goto_0

    :cond_0
    iget-object v1, p0, Lcom/bytedance/sdk/component/ex/Fj/Fj/Fj/Fj$2;->Fj:Lcom/bytedance/sdk/component/ex/Fj/hjc;

    iget-object v2, p0, Lcom/bytedance/sdk/component/ex/Fj/Fj/Fj/Fj$2;->ex:Lcom/bytedance/sdk/component/ex/Fj/Fj/Fj/Fj;

    invoke-interface {v1, v2, v0}, Lcom/bytedance/sdk/component/ex/Fj/hjc;->Fj(Lcom/bytedance/sdk/component/ex/Fj/ex;Lcom/bytedance/sdk/component/ex/Fj/JW;)V
    :try_end_0
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_0

    return-void

    :goto_0
    iget-object v1, p0, Lcom/bytedance/sdk/component/ex/Fj/Fj/Fj/Fj$2;->Fj:Lcom/bytedance/sdk/component/ex/Fj/hjc;

    iget-object v2, p0, Lcom/bytedance/sdk/component/ex/Fj/Fj/Fj/Fj$2;->ex:Lcom/bytedance/sdk/component/ex/Fj/Fj/Fj/Fj;

    invoke-interface {v1, v2, v0}, Lcom/bytedance/sdk/component/ex/Fj/hjc;->Fj(Lcom/bytedance/sdk/component/ex/Fj/ex;Ljava/io/IOException;)V

    return-void
.end method
