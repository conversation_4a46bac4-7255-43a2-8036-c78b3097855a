.class public Lcom/bytedance/sdk/component/ex/Fj/Fj/Fj/ex;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/sdk/component/ex/Fj/BcC$Fj;


# instance fields
.field Fj:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lcom/bytedance/sdk/component/ex/Fj/BcC;",
            ">;"
        }
    .end annotation
.end field

.field ex:Lcom/bytedance/sdk/component/ex/Fj/dG;

.field hjc:I


# direct methods
.method public constructor <init>(Ljava/util/List;Lcom/bytedance/sdk/component/ex/Fj/dG;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lcom/bytedance/sdk/component/ex/Fj/BcC;",
            ">;",
            "Lcom/bytedance/sdk/component/ex/Fj/dG;",
            ")V"
        }
    .end annotation

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, 0x0

    iput v0, p0, Lcom/bytedance/sdk/component/ex/Fj/Fj/Fj/ex;->hjc:I

    iput-object p1, p0, Lcom/bytedance/sdk/component/ex/Fj/Fj/Fj/ex;->Fj:Ljava/util/List;

    iput-object p2, p0, Lcom/bytedance/sdk/component/ex/Fj/Fj/Fj/ex;->ex:Lcom/bytedance/sdk/component/ex/Fj/dG;

    return-void
.end method


# virtual methods
.method public Fj(Lcom/bytedance/sdk/component/ex/Fj/dG;)Lcom/bytedance/sdk/component/ex/Fj/JW;
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iput-object p1, p0, Lcom/bytedance/sdk/component/ex/Fj/Fj/Fj/ex;->ex:Lcom/bytedance/sdk/component/ex/Fj/dG;

    iget p1, p0, Lcom/bytedance/sdk/component/ex/Fj/Fj/Fj/ex;->hjc:I

    add-int/lit8 p1, p1, 0x1

    iput p1, p0, Lcom/bytedance/sdk/component/ex/Fj/Fj/Fj/ex;->hjc:I

    iget-object v0, p0, Lcom/bytedance/sdk/component/ex/Fj/Fj/Fj/ex;->Fj:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    if-lt p1, v0, :cond_0

    const/4 p1, 0x0

    return-object p1

    :cond_0
    iget-object p1, p0, Lcom/bytedance/sdk/component/ex/Fj/Fj/Fj/ex;->Fj:Ljava/util/List;

    iget v0, p0, Lcom/bytedance/sdk/component/ex/Fj/Fj/Fj/ex;->hjc:I

    invoke-interface {p1, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/bytedance/sdk/component/ex/Fj/BcC;

    invoke-interface {p1, p0}, Lcom/bytedance/sdk/component/ex/Fj/BcC;->Fj(Lcom/bytedance/sdk/component/ex/Fj/BcC$Fj;)Lcom/bytedance/sdk/component/ex/Fj/JW;

    move-result-object p1

    return-object p1
.end method

.method public Fj()Lcom/bytedance/sdk/component/ex/Fj/dG;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/ex/Fj/Fj/Fj/ex;->ex:Lcom/bytedance/sdk/component/ex/Fj/dG;

    return-object v0
.end method
