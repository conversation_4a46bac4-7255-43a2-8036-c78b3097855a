.class public Lcom/bytedance/sdk/component/ex/Fj/Fj/Fj/hjc;
.super Lcom/bytedance/sdk/component/ex/Fj/rAx;


# instance fields
.field public BcC:Lcom/bytedance/sdk/component/ex/Fj/Fj/Fj/eV;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/component/ex/Fj/rAx$Fj;)V
    .locals 0

    invoke-direct {p0, p1}, Lcom/bytedance/sdk/component/ex/Fj/rAx;-><init>(Lcom/bytedance/sdk/component/ex/Fj/rAx$Fj;)V

    new-instance p1, Lcom/bytedance/sdk/component/ex/Fj/Fj/Fj/eV;

    invoke-direct {p1}, Lcom/bytedance/sdk/component/ex/Fj/Fj/Fj/eV;-><init>()V

    iput-object p1, p0, Lcom/bytedance/sdk/component/ex/Fj/Fj/Fj/hjc;->BcC:Lcom/bytedance/sdk/component/ex/Fj/Fj/Fj/eV;

    return-void
.end method


# virtual methods
.method public Fj()Lcom/bytedance/sdk/component/ex/Fj/eV;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/ex/Fj/Fj/Fj/hjc;->BcC:Lcom/bytedance/sdk/component/ex/Fj/Fj/Fj/eV;

    return-object v0
.end method

.method public Fj(Lcom/bytedance/sdk/component/ex/Fj/dG;)Lcom/bytedance/sdk/component/ex/Fj/ex;
    .locals 2

    invoke-virtual {p1, p0}, Lcom/bytedance/sdk/component/ex/Fj/dG;->Fj(Lcom/bytedance/sdk/component/ex/Fj/rAx;)V

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/ex/Fj/dG;->ex()Lcom/bytedance/sdk/component/ex/Fj/svN;

    move-result-object v0

    if-eqz v0, :cond_1

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/ex/Fj/dG;->ex()Lcom/bytedance/sdk/component/ex/Fj/svN;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/ex/Fj/svN;->Fj()Ljava/net/URL;

    move-result-object v0

    if-eqz v0, :cond_1

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/ex/Fj/dG;->ex()Lcom/bytedance/sdk/component/ex/Fj/svN;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/ex/Fj/svN;->Fj()Ljava/net/URL;

    move-result-object v0

    invoke-virtual {v0}, Ljava/net/URL;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-eqz v0, :cond_0

    goto :goto_0

    :cond_0
    new-instance v0, Lcom/bytedance/sdk/component/ex/Fj/Fj/Fj/Fj;

    iget-object v1, p0, Lcom/bytedance/sdk/component/ex/Fj/Fj/Fj/hjc;->BcC:Lcom/bytedance/sdk/component/ex/Fj/Fj/Fj/eV;

    invoke-direct {v0, p1, v1}, Lcom/bytedance/sdk/component/ex/Fj/Fj/Fj/Fj;-><init>(Lcom/bytedance/sdk/component/ex/Fj/dG;Lcom/bytedance/sdk/component/ex/Fj/eV;)V

    iget-object p1, p0, Lcom/bytedance/sdk/component/ex/Fj/Fj/Fj/hjc;->BcC:Lcom/bytedance/sdk/component/ex/Fj/Fj/Fj/eV;

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/ex/Fj/Fj/Fj/eV;->hjc()Ljava/util/List;

    move-result-object p1

    invoke-interface {p1, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-object v0

    :cond_1
    :goto_0
    const/4 p1, 0x0

    return-object p1
.end method
