.class public abstract Lcom/bytedance/sdk/component/ex/Fj/JU;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/io/Closeable;


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public abstract Fj()J
.end method

.method public abstract Ubf()Lcom/bytedance/sdk/component/ex/Fj/mSE;
.end method

.method public abstract close()V
.end method

.method public abstract eV()[B
.end method

.method public abstract ex()Ljava/lang/String;
.end method

.method public abstract hjc()Ljava/io/InputStream;
.end method
