.class public abstract Lcom/bytedance/sdk/component/ex/Fj/JW;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/io/Closeable;


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public abstract BcC()Lcom/bytedance/sdk/component/ex/Fj/UYd;
.end method

.method public abstract Fj()J
.end method

.method public abstract Fj(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;
.end method

.method public abstract Ubf()Ljava/lang/String;
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation
.end method

.method public abstract WR()Lcom/bytedance/sdk/component/ex/Fj/JU;
.end method

.method public abstract close()V
.end method

.method public abstract eV()Z
.end method

.method public abstract ex()J
.end method

.method public abstract hjc()I
.end method

.method public abstract mSE()Lcom/bytedance/sdk/component/ex/Fj/Ko;
.end method

.method public abstract svN()Lcom/bytedance/sdk/component/ex/Fj/WR;
.end method
