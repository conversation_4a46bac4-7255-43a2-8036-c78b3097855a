.class public final enum Lcom/bytedance/sdk/component/ex/Fj/Tc$Fj;
.super Ljava/lang/Enum;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bytedance/sdk/component/ex/Fj/Tc;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x4019
    name = "Fj"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "Lcom/bytedance/sdk/component/ex/Fj/Tc$Fj;",
        ">;"
    }
.end annotation


# static fields
.field public static final enum Fj:Lcom/bytedance/sdk/component/ex/Fj/Tc$Fj;

.field public static final enum ex:Lcom/bytedance/sdk/component/ex/Fj/Tc$Fj;

.field private static final synthetic hjc:[Lcom/bytedance/sdk/component/ex/Fj/Tc$Fj;


# direct methods
.method static constructor <clinit>()V
    .locals 5

    new-instance v0, Lcom/bytedance/sdk/component/ex/Fj/Tc$Fj;

    const-string v1, "STRING_TYPE"

    const/4 v2, 0x0

    invoke-direct {v0, v1, v2}, Lcom/bytedance/sdk/component/ex/Fj/Tc$Fj;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lcom/bytedance/sdk/component/ex/Fj/Tc$Fj;->Fj:Lcom/bytedance/sdk/component/ex/Fj/Tc$Fj;

    new-instance v1, Lcom/bytedance/sdk/component/ex/Fj/Tc$Fj;

    const-string v3, "BYTE_ARRAY_TYPE"

    const/4 v4, 0x1

    invoke-direct {v1, v3, v4}, Lcom/bytedance/sdk/component/ex/Fj/Tc$Fj;-><init>(Ljava/lang/String;I)V

    sput-object v1, Lcom/bytedance/sdk/component/ex/Fj/Tc$Fj;->ex:Lcom/bytedance/sdk/component/ex/Fj/Tc$Fj;

    const/4 v3, 0x2

    new-array v3, v3, [Lcom/bytedance/sdk/component/ex/Fj/Tc$Fj;

    aput-object v0, v3, v2

    aput-object v1, v3, v4

    sput-object v3, Lcom/bytedance/sdk/component/ex/Fj/Tc$Fj;->hjc:[Lcom/bytedance/sdk/component/ex/Fj/Tc$Fj;

    return-void
.end method

.method private constructor <init>(Ljava/lang/String;I)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation

    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    return-void
.end method

.method public static valueOf(Ljava/lang/String;)Lcom/bytedance/sdk/component/ex/Fj/Tc$Fj;
    .locals 1

    const-class v0, Lcom/bytedance/sdk/component/ex/Fj/Tc$Fj;

    invoke-static {v0, p0}, Ljava/lang/Enum;->valueOf(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Enum;

    move-result-object p0

    check-cast p0, Lcom/bytedance/sdk/component/ex/Fj/Tc$Fj;

    return-object p0
.end method

.method public static values()[Lcom/bytedance/sdk/component/ex/Fj/Tc$Fj;
    .locals 1

    sget-object v0, Lcom/bytedance/sdk/component/ex/Fj/Tc$Fj;->hjc:[Lcom/bytedance/sdk/component/ex/Fj/Tc$Fj;

    invoke-virtual {v0}, [Lcom/bytedance/sdk/component/ex/Fj/Tc$Fj;->clone()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Lcom/bytedance/sdk/component/ex/Fj/Tc$Fj;

    return-object v0
.end method
