.class public Lcom/bytedance/sdk/component/ex/Fj/Tc;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/bytedance/sdk/component/ex/Fj/Tc$Fj;
    }
.end annotation


# instance fields
.field public Ubf:[B

.field public WR:Lcom/bytedance/sdk/component/ex/Fj/Tc$Fj;

.field public eV:Ljava/lang/String;

.field public hjc:Lcom/bytedance/sdk/component/ex/Fj/mSE;


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public constructor <init>(Lcom/bytedance/sdk/component/ex/Fj/mSE;Ljava/lang/String;Lcom/bytedance/sdk/component/ex/Fj/Tc$Fj;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/bytedance/sdk/component/ex/Fj/Tc;->hjc:Lcom/bytedance/sdk/component/ex/Fj/mSE;

    iput-object p2, p0, Lcom/bytedance/sdk/component/ex/Fj/Tc;->eV:Ljava/lang/String;

    iput-object p3, p0, Lcom/bytedance/sdk/component/ex/Fj/Tc;->WR:Lcom/bytedance/sdk/component/ex/Fj/Tc$Fj;

    return-void
.end method

.method public constructor <init>(Lcom/bytedance/sdk/component/ex/Fj/mSE;[BLcom/bytedance/sdk/component/ex/Fj/Tc$Fj;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/bytedance/sdk/component/ex/Fj/Tc;->hjc:Lcom/bytedance/sdk/component/ex/Fj/mSE;

    iput-object p2, p0, Lcom/bytedance/sdk/component/ex/Fj/Tc;->Ubf:[B

    iput-object p3, p0, Lcom/bytedance/sdk/component/ex/Fj/Tc;->WR:Lcom/bytedance/sdk/component/ex/Fj/Tc$Fj;

    return-void
.end method

.method public static Fj(Lcom/bytedance/sdk/component/ex/Fj/mSE;Ljava/lang/String;)Lcom/bytedance/sdk/component/ex/Fj/Tc;
    .locals 2

    new-instance v0, Lcom/bytedance/sdk/component/ex/Fj/Tc;

    sget-object v1, Lcom/bytedance/sdk/component/ex/Fj/Tc$Fj;->Fj:Lcom/bytedance/sdk/component/ex/Fj/Tc$Fj;

    invoke-direct {v0, p0, p1, v1}, Lcom/bytedance/sdk/component/ex/Fj/Tc;-><init>(Lcom/bytedance/sdk/component/ex/Fj/mSE;Ljava/lang/String;Lcom/bytedance/sdk/component/ex/Fj/Tc$Fj;)V

    return-object v0
.end method

.method public static Fj(Lcom/bytedance/sdk/component/ex/Fj/mSE;[B)Lcom/bytedance/sdk/component/ex/Fj/Tc;
    .locals 2

    new-instance v0, Lcom/bytedance/sdk/component/ex/Fj/Tc;

    sget-object v1, Lcom/bytedance/sdk/component/ex/Fj/Tc$Fj;->ex:Lcom/bytedance/sdk/component/ex/Fj/Tc$Fj;

    invoke-direct {v0, p0, p1, v1}, Lcom/bytedance/sdk/component/ex/Fj/Tc;-><init>(Lcom/bytedance/sdk/component/ex/Fj/mSE;[BLcom/bytedance/sdk/component/ex/Fj/Tc$Fj;)V

    return-object v0
.end method


# virtual methods
.method public Fj()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/ex/Fj/Tc;->eV:Ljava/lang/String;

    return-object v0
.end method
