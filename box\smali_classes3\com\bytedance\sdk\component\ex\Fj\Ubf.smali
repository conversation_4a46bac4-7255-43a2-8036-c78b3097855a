.class public final Lcom/bytedance/sdk/component/ex/Fj/Ubf;
.super Lcom/bytedance/sdk/component/ex/Fj/Tc;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/bytedance/sdk/component/ex/Fj/Ubf$Fj;
    }
.end annotation


# instance fields
.field Fj:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field ex:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(Ljava/util/List;Ljava/util/List;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;)V"
        }
    .end annotation

    invoke-direct {p0}, Lcom/bytedance/sdk/component/ex/Fj/Tc;-><init>()V

    iput-object p1, p0, Lcom/bytedance/sdk/component/ex/Fj/Ubf;->Fj:Ljava/util/List;

    iput-object p2, p0, Lcom/bytedance/sdk/component/ex/Fj/Ubf;->ex:Ljava/util/List;

    return-void
.end method
