.class Lcom/bytedance/sdk/component/ex/Fj/dG$Fj$1;
.super Lcom/bytedance/sdk/component/ex/Fj/dG;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/component/ex/Fj/dG$Fj;->ex()Lcom/bytedance/sdk/component/ex/Fj/dG;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic ex:Lcom/bytedance/sdk/component/ex/Fj/dG$Fj;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/component/ex/Fj/dG$Fj;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/ex/Fj/dG$Fj$1;->ex:Lcom/bytedance/sdk/component/ex/Fj/dG$Fj;

    invoke-direct {p0}, Lcom/bytedance/sdk/component/ex/Fj/dG;-><init>()V

    return-void
.end method


# virtual methods
.method public Fj()Ljava/lang/Object;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/ex/Fj/dG$Fj$1;->ex:Lcom/bytedance/sdk/component/ex/Fj/dG$Fj;

    iget-object v0, v0, Lcom/bytedance/sdk/component/ex/Fj/dG$Fj;->Ubf:Ljava/lang/Object;

    return-object v0
.end method

.method public Ubf()Lcom/bytedance/sdk/component/ex/Fj/Fj;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/ex/Fj/dG$Fj$1;->ex:Lcom/bytedance/sdk/component/ex/Fj/dG$Fj;

    iget-object v0, v0, Lcom/bytedance/sdk/component/ex/Fj/dG$Fj;->Fj:Lcom/bytedance/sdk/component/ex/Fj/Fj;

    return-object v0
.end method

.method public WR()Lcom/bytedance/sdk/component/ex/Fj/Tc;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/ex/Fj/dG$Fj$1;->ex:Lcom/bytedance/sdk/component/ex/Fj/dG$Fj;

    iget-object v0, v0, Lcom/bytedance/sdk/component/ex/Fj/dG$Fj;->WR:Lcom/bytedance/sdk/component/ex/Fj/Tc;

    return-object v0
.end method

.method public eV()Ljava/util/Map;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/ex/Fj/dG$Fj$1;->ex:Lcom/bytedance/sdk/component/ex/Fj/dG$Fj;

    iget-object v0, v0, Lcom/bytedance/sdk/component/ex/Fj/dG$Fj;->ex:Ljava/util/Map;

    return-object v0
.end method

.method public ex()Lcom/bytedance/sdk/component/ex/Fj/svN;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/ex/Fj/dG$Fj$1;->ex:Lcom/bytedance/sdk/component/ex/Fj/dG$Fj;

    iget-object v0, v0, Lcom/bytedance/sdk/component/ex/Fj/dG$Fj;->hjc:Lcom/bytedance/sdk/component/ex/Fj/svN;

    return-object v0
.end method

.method public hjc()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/ex/Fj/dG$Fj$1;->ex:Lcom/bytedance/sdk/component/ex/Fj/dG$Fj;

    iget-object v0, v0, Lcom/bytedance/sdk/component/ex/Fj/dG$Fj;->eV:Ljava/lang/String;

    return-object v0
.end method

.method public toString()Ljava/lang/String;
    .locals 1

    const-string v0, ""

    return-object v0
.end method
