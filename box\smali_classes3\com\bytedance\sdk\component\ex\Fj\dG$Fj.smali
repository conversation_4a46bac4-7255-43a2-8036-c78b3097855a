.class public Lcom/bytedance/sdk/component/ex/Fj/dG$Fj;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bytedance/sdk/component/ex/Fj/dG;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "Fj"
.end annotation


# instance fields
.field Fj:Lcom/bytedance/sdk/component/ex/Fj/Fj;

.field Ubf:Ljava/lang/Object;

.field WR:Lcom/bytedance/sdk/component/ex/Fj/Tc;

.field eV:Ljava/lang/String;

.field ex:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;>;"
        }
    .end annotation
.end field

.field hjc:Lcom/bytedance/sdk/component/ex/Fj/svN;


# direct methods
.method public constructor <init>()V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    iput-object v0, p0, Lcom/bytedance/sdk/component/ex/Fj/dG$Fj;->ex:Ljava/util/Map;

    return-void
.end method

.method public constructor <init>(Lcom/bytedance/sdk/component/ex/Fj/dG;)V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/ex/Fj/dG;->ex()Lcom/bytedance/sdk/component/ex/Fj/svN;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/sdk/component/ex/Fj/dG$Fj;->hjc:Lcom/bytedance/sdk/component/ex/Fj/svN;

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/ex/Fj/dG;->hjc()Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/sdk/component/ex/Fj/dG$Fj;->eV:Ljava/lang/String;

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/ex/Fj/dG;->eV()Ljava/util/Map;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/sdk/component/ex/Fj/dG$Fj;->ex:Ljava/util/Map;

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/ex/Fj/dG;->Fj()Ljava/lang/Object;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/sdk/component/ex/Fj/dG$Fj;->Ubf:Ljava/lang/Object;

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/ex/Fj/dG;->WR()Lcom/bytedance/sdk/component/ex/Fj/Tc;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/sdk/component/ex/Fj/dG$Fj;->WR:Lcom/bytedance/sdk/component/ex/Fj/Tc;

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/ex/Fj/dG;->Ubf()Lcom/bytedance/sdk/component/ex/Fj/Fj;

    move-result-object p1

    iput-object p1, p0, Lcom/bytedance/sdk/component/ex/Fj/dG$Fj;->Fj:Lcom/bytedance/sdk/component/ex/Fj/Fj;

    return-void
.end method

.method private Fj(Ljava/lang/String;Lcom/bytedance/sdk/component/ex/Fj/Tc;)Lcom/bytedance/sdk/component/ex/Fj/dG$Fj;
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/ex/Fj/dG$Fj;->eV:Ljava/lang/String;

    iput-object p2, p0, Lcom/bytedance/sdk/component/ex/Fj/dG$Fj;->WR:Lcom/bytedance/sdk/component/ex/Fj/Tc;

    return-object p0
.end method


# virtual methods
.method public Fj()Lcom/bytedance/sdk/component/ex/Fj/dG$Fj;
    .locals 2

    const-string v0, "GET"

    const/4 v1, 0x0

    invoke-direct {p0, v0, v1}, Lcom/bytedance/sdk/component/ex/Fj/dG$Fj;->Fj(Ljava/lang/String;Lcom/bytedance/sdk/component/ex/Fj/Tc;)Lcom/bytedance/sdk/component/ex/Fj/dG$Fj;

    move-result-object v0

    return-object v0
.end method

.method public Fj(Lcom/bytedance/sdk/component/ex/Fj/Fj;)Lcom/bytedance/sdk/component/ex/Fj/dG$Fj;
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/ex/Fj/dG$Fj;->Fj:Lcom/bytedance/sdk/component/ex/Fj/Fj;

    return-object p0
.end method

.method public Fj(Lcom/bytedance/sdk/component/ex/Fj/Tc;)Lcom/bytedance/sdk/component/ex/Fj/dG$Fj;
    .locals 1

    const-string v0, "POST"

    invoke-direct {p0, v0, p1}, Lcom/bytedance/sdk/component/ex/Fj/dG$Fj;->Fj(Ljava/lang/String;Lcom/bytedance/sdk/component/ex/Fj/Tc;)Lcom/bytedance/sdk/component/ex/Fj/dG$Fj;

    move-result-object p1

    return-object p1
.end method

.method public Fj(Lcom/bytedance/sdk/component/ex/Fj/svN;)Lcom/bytedance/sdk/component/ex/Fj/dG$Fj;
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/ex/Fj/dG$Fj;->hjc:Lcom/bytedance/sdk/component/ex/Fj/svN;

    return-object p0
.end method

.method public Fj(Ljava/lang/Object;)Lcom/bytedance/sdk/component/ex/Fj/dG$Fj;
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/ex/Fj/dG$Fj;->Ubf:Ljava/lang/Object;

    return-object p0
.end method

.method public Fj(Ljava/lang/String;)Lcom/bytedance/sdk/component/ex/Fj/dG$Fj;
    .locals 0

    invoke-static {p1}, Lcom/bytedance/sdk/component/ex/Fj/svN;->hjc(Ljava/lang/String;)Lcom/bytedance/sdk/component/ex/Fj/svN;

    move-result-object p1

    invoke-virtual {p0, p1}, Lcom/bytedance/sdk/component/ex/Fj/dG$Fj;->Fj(Lcom/bytedance/sdk/component/ex/Fj/svN;)Lcom/bytedance/sdk/component/ex/Fj/dG$Fj;

    move-result-object p1

    return-object p1
.end method

.method public Fj(Ljava/lang/String;Ljava/lang/String;)Lcom/bytedance/sdk/component/ex/Fj/dG$Fj;
    .locals 0

    invoke-virtual {p0, p1, p2}, Lcom/bytedance/sdk/component/ex/Fj/dG$Fj;->ex(Ljava/lang/String;Ljava/lang/String;)Lcom/bytedance/sdk/component/ex/Fj/dG$Fj;

    move-result-object p1

    return-object p1
.end method

.method public ex(Ljava/lang/String;Ljava/lang/String;)Lcom/bytedance/sdk/component/ex/Fj/dG$Fj;
    .locals 2

    iget-object v0, p0, Lcom/bytedance/sdk/component/ex/Fj/dG$Fj;->ex:Ljava/util/Map;

    invoke-interface {v0, p1}, Ljava/util/Map;->containsKey(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/sdk/component/ex/Fj/dG$Fj;->ex:Ljava/util/Map;

    new-instance v1, Ljava/util/ArrayList;

    invoke-direct {v1}, Ljava/util/ArrayList;-><init>()V

    invoke-interface {v0, p1, v1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    :cond_0
    iget-object v0, p0, Lcom/bytedance/sdk/component/ex/Fj/dG$Fj;->ex:Ljava/util/Map;

    invoke-interface {v0, p1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/util/List;

    invoke-interface {p1, p2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-object p0
.end method

.method public ex()Lcom/bytedance/sdk/component/ex/Fj/dG;
    .locals 1

    new-instance v0, Lcom/bytedance/sdk/component/ex/Fj/dG$Fj$1;

    invoke-direct {v0, p0}, Lcom/bytedance/sdk/component/ex/Fj/dG$Fj$1;-><init>(Lcom/bytedance/sdk/component/ex/Fj/dG$Fj;)V

    return-object v0
.end method
