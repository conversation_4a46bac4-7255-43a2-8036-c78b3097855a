.class public abstract Lcom/bytedance/sdk/component/ex/Fj/dG;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/bytedance/sdk/component/ex/Fj/dG$Fj;
    }
.end annotation


# instance fields
.field public Fj:Lcom/bytedance/sdk/component/ex/Fj/rAx;


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public abstract Fj()Ljava/lang/Object;
.end method

.method public Fj(Lcom/bytedance/sdk/component/ex/Fj/rAx;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/ex/Fj/dG;->Fj:Lcom/bytedance/sdk/component/ex/Fj/rAx;

    return-void
.end method

.method public abstract Ubf()Lcom/bytedance/sdk/component/ex/Fj/Fj;
.end method

.method public WR()Lcom/bytedance/sdk/component/ex/Fj/Tc;
    .locals 1

    const/4 v0, 0x0

    return-object v0
.end method

.method public abstract eV()Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;>;"
        }
    .end annotation
.end method

.method public abstract ex()Lcom/bytedance/sdk/component/ex/Fj/svN;
.end method

.method public abstract hjc()Ljava/lang/String;
.end method

.method public svN()Lcom/bytedance/sdk/component/ex/Fj/dG$Fj;
    .locals 1

    new-instance v0, Lcom/bytedance/sdk/component/ex/Fj/dG$Fj;

    invoke-direct {v0, p0}, Lcom/bytedance/sdk/component/ex/Fj/dG$Fj;-><init>(Lcom/bytedance/sdk/component/ex/Fj/dG;)V

    return-object v0
.end method
