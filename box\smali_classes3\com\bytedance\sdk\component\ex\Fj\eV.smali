.class public abstract Lcom/bytedance/sdk/component/ex/Fj/eV;
.super Ljava/lang/Object;


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public Fj()I
    .locals 1

    const/16 v0, 0x40

    return v0
.end method

.method public abstract Fj(I)V
.end method

.method public abstract eV()Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lcom/bytedance/sdk/component/ex/Fj/ex;",
            ">;"
        }
    .end annotation
.end method

.method public ex()Ljava/util/concurrent/ExecutorService;
    .locals 1

    const/4 v0, 0x0

    return-object v0
.end method

.method public abstract hjc()Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lcom/bytedance/sdk/component/ex/Fj/ex;",
            ">;"
        }
    .end annotation
.end method
