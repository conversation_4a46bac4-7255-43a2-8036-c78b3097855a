.class public interface abstract Lcom/bytedance/sdk/component/ex/Fj/ex;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/lang/Cloneable;


# virtual methods
.method public abstract Fj()Lcom/bytedance/sdk/component/ex/Fj/JW;
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation
.end method

.method public abstract Fj(Lcom/bytedance/sdk/component/ex/Fj/hjc;)V
.end method
