.class final Lcom/bytedance/sdk/component/ex/Fj/ex/Ubf;
.super Ljava/lang/Object;


# instance fields
.field final Fj:[B

.field Ubf:Z

.field WR:Lcom/bytedance/sdk/component/ex/Fj/ex/Ubf;

.field eV:Z

.field ex:I

.field hjc:I

.field svN:Lcom/bytedance/sdk/component/ex/Fj/ex/Ubf;


# direct methods
.method public constructor <init>()V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/16 v0, 0x2000

    new-array v0, v0, [B

    iput-object v0, p0, Lcom/bytedance/sdk/component/ex/Fj/ex/Ubf;->Fj:[B

    const/4 v0, 0x1

    iput-boolean v0, p0, Lcom/bytedance/sdk/component/ex/Fj/ex/Ubf;->Ubf:Z

    const/4 v0, 0x0

    iput-boolean v0, p0, Lcom/bytedance/sdk/component/ex/Fj/ex/Ubf;->eV:Z

    return-void
.end method

.method public constructor <init>([BIIZZ)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/bytedance/sdk/component/ex/Fj/ex/Ubf;->Fj:[B

    iput p2, p0, Lcom/bytedance/sdk/component/ex/Fj/ex/Ubf;->ex:I

    iput p3, p0, Lcom/bytedance/sdk/component/ex/Fj/ex/Ubf;->hjc:I

    iput-boolean p4, p0, Lcom/bytedance/sdk/component/ex/Fj/ex/Ubf;->eV:Z

    iput-boolean p5, p0, Lcom/bytedance/sdk/component/ex/Fj/ex/Ubf;->Ubf:Z

    return-void
.end method


# virtual methods
.method public final Fj()Lcom/bytedance/sdk/component/ex/Fj/ex/Ubf;
    .locals 7

    const/4 v0, 0x1

    iput-boolean v0, p0, Lcom/bytedance/sdk/component/ex/Fj/ex/Ubf;->eV:Z

    new-instance v0, Lcom/bytedance/sdk/component/ex/Fj/ex/Ubf;

    iget-object v2, p0, Lcom/bytedance/sdk/component/ex/Fj/ex/Ubf;->Fj:[B

    iget v3, p0, Lcom/bytedance/sdk/component/ex/Fj/ex/Ubf;->ex:I

    iget v4, p0, Lcom/bytedance/sdk/component/ex/Fj/ex/Ubf;->hjc:I

    const/4 v5, 0x1

    const/4 v6, 0x0

    move-object v1, v0

    invoke-direct/range {v1 .. v6}, Lcom/bytedance/sdk/component/ex/Fj/ex/Ubf;-><init>([BIIZZ)V

    return-object v0
.end method

.method public final Fj(Lcom/bytedance/sdk/component/ex/Fj/ex/Ubf;)Lcom/bytedance/sdk/component/ex/Fj/ex/Ubf;
    .locals 1

    iput-object p0, p1, Lcom/bytedance/sdk/component/ex/Fj/ex/Ubf;->svN:Lcom/bytedance/sdk/component/ex/Fj/ex/Ubf;

    iget-object v0, p0, Lcom/bytedance/sdk/component/ex/Fj/ex/Ubf;->WR:Lcom/bytedance/sdk/component/ex/Fj/ex/Ubf;

    iput-object v0, p1, Lcom/bytedance/sdk/component/ex/Fj/ex/Ubf;->WR:Lcom/bytedance/sdk/component/ex/Fj/ex/Ubf;

    iget-object v0, p0, Lcom/bytedance/sdk/component/ex/Fj/ex/Ubf;->WR:Lcom/bytedance/sdk/component/ex/Fj/ex/Ubf;

    iput-object p1, v0, Lcom/bytedance/sdk/component/ex/Fj/ex/Ubf;->svN:Lcom/bytedance/sdk/component/ex/Fj/ex/Ubf;

    iput-object p1, p0, Lcom/bytedance/sdk/component/ex/Fj/ex/Ubf;->WR:Lcom/bytedance/sdk/component/ex/Fj/ex/Ubf;

    return-object p1
.end method

.method public final ex()Lcom/bytedance/sdk/component/ex/Fj/ex/Ubf;
    .locals 4

    iget-object v0, p0, Lcom/bytedance/sdk/component/ex/Fj/ex/Ubf;->WR:Lcom/bytedance/sdk/component/ex/Fj/ex/Ubf;

    const/4 v1, 0x0

    if-eq v0, p0, :cond_0

    move-object v2, v0

    goto :goto_0

    :cond_0
    move-object v2, v1

    :goto_0
    iget-object v3, p0, Lcom/bytedance/sdk/component/ex/Fj/ex/Ubf;->svN:Lcom/bytedance/sdk/component/ex/Fj/ex/Ubf;

    if-eqz v3, :cond_1

    iput-object v0, v3, Lcom/bytedance/sdk/component/ex/Fj/ex/Ubf;->WR:Lcom/bytedance/sdk/component/ex/Fj/ex/Ubf;

    :cond_1
    iget-object v0, p0, Lcom/bytedance/sdk/component/ex/Fj/ex/Ubf;->WR:Lcom/bytedance/sdk/component/ex/Fj/ex/Ubf;

    if-eqz v0, :cond_2

    iput-object v3, v0, Lcom/bytedance/sdk/component/ex/Fj/ex/Ubf;->svN:Lcom/bytedance/sdk/component/ex/Fj/ex/Ubf;

    :cond_2
    iput-object v1, p0, Lcom/bytedance/sdk/component/ex/Fj/ex/Ubf;->WR:Lcom/bytedance/sdk/component/ex/Fj/ex/Ubf;

    iput-object v1, p0, Lcom/bytedance/sdk/component/ex/Fj/ex/Ubf;->svN:Lcom/bytedance/sdk/component/ex/Fj/ex/Ubf;

    return-object v2
.end method
