.class public final Lcom/bytedance/sdk/component/ex/Fj/rAx$Fj;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bytedance/sdk/component/ex/Fj/rAx;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "Fj"
.end annotation


# instance fields
.field public final Fj:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lcom/bytedance/sdk/component/ex/Fj/BcC;",
            ">;"
        }
    .end annotation
.end field

.field public Ubf:Ljava/util/concurrent/TimeUnit;

.field public WR:J

.field public eV:J

.field public ex:J

.field public hjc:Ljava/util/concurrent/TimeUnit;

.field public svN:Ljava/util/concurrent/TimeUnit;


# direct methods
.method public constructor <init>()V
    .locals 3

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lcom/bytedance/sdk/component/ex/Fj/rAx$Fj;->Fj:Ljava/util/List;

    const-wide/16 v0, 0x2710

    iput-wide v0, p0, Lcom/bytedance/sdk/component/ex/Fj/rAx$Fj;->ex:J

    sget-object v2, Ljava/util/concurrent/TimeUnit;->MILLISECONDS:Ljava/util/concurrent/TimeUnit;

    iput-object v2, p0, Lcom/bytedance/sdk/component/ex/Fj/rAx$Fj;->hjc:Ljava/util/concurrent/TimeUnit;

    iput-wide v0, p0, Lcom/bytedance/sdk/component/ex/Fj/rAx$Fj;->eV:J

    iput-object v2, p0, Lcom/bytedance/sdk/component/ex/Fj/rAx$Fj;->Ubf:Ljava/util/concurrent/TimeUnit;

    iput-wide v0, p0, Lcom/bytedance/sdk/component/ex/Fj/rAx$Fj;->WR:J

    iput-object v2, p0, Lcom/bytedance/sdk/component/ex/Fj/rAx$Fj;->svN:Ljava/util/concurrent/TimeUnit;

    return-void
.end method

.method public constructor <init>(Lcom/bytedance/sdk/component/ex/Fj/rAx;)V
    .locals 3

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lcom/bytedance/sdk/component/ex/Fj/rAx$Fj;->Fj:Ljava/util/List;

    const-wide/16 v0, 0x2710

    iput-wide v0, p0, Lcom/bytedance/sdk/component/ex/Fj/rAx$Fj;->ex:J

    sget-object v2, Ljava/util/concurrent/TimeUnit;->MILLISECONDS:Ljava/util/concurrent/TimeUnit;

    iput-object v2, p0, Lcom/bytedance/sdk/component/ex/Fj/rAx$Fj;->hjc:Ljava/util/concurrent/TimeUnit;

    iput-wide v0, p0, Lcom/bytedance/sdk/component/ex/Fj/rAx$Fj;->eV:J

    iput-object v2, p0, Lcom/bytedance/sdk/component/ex/Fj/rAx$Fj;->Ubf:Ljava/util/concurrent/TimeUnit;

    iput-wide v0, p0, Lcom/bytedance/sdk/component/ex/Fj/rAx$Fj;->WR:J

    iput-object v2, p0, Lcom/bytedance/sdk/component/ex/Fj/rAx$Fj;->svN:Ljava/util/concurrent/TimeUnit;

    iget-wide v0, p1, Lcom/bytedance/sdk/component/ex/Fj/rAx;->ex:J

    iput-wide v0, p0, Lcom/bytedance/sdk/component/ex/Fj/rAx$Fj;->ex:J

    iget-object v0, p1, Lcom/bytedance/sdk/component/ex/Fj/rAx;->hjc:Ljava/util/concurrent/TimeUnit;

    iput-object v0, p0, Lcom/bytedance/sdk/component/ex/Fj/rAx$Fj;->hjc:Ljava/util/concurrent/TimeUnit;

    iget-wide v0, p1, Lcom/bytedance/sdk/component/ex/Fj/rAx;->eV:J

    iput-wide v0, p0, Lcom/bytedance/sdk/component/ex/Fj/rAx$Fj;->eV:J

    iget-object v0, p1, Lcom/bytedance/sdk/component/ex/Fj/rAx;->Ubf:Ljava/util/concurrent/TimeUnit;

    iput-object v0, p0, Lcom/bytedance/sdk/component/ex/Fj/rAx$Fj;->Ubf:Ljava/util/concurrent/TimeUnit;

    iget-wide v0, p1, Lcom/bytedance/sdk/component/ex/Fj/rAx;->WR:J

    iput-wide v0, p0, Lcom/bytedance/sdk/component/ex/Fj/rAx$Fj;->WR:J

    iget-object p1, p1, Lcom/bytedance/sdk/component/ex/Fj/rAx;->svN:Ljava/util/concurrent/TimeUnit;

    iput-object p1, p0, Lcom/bytedance/sdk/component/ex/Fj/rAx$Fj;->svN:Ljava/util/concurrent/TimeUnit;

    return-void
.end method

.method public constructor <init>(Ljava/lang/String;)V
    .locals 2

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    new-instance p1, Ljava/util/ArrayList;

    invoke-direct {p1}, Ljava/util/ArrayList;-><init>()V

    iput-object p1, p0, Lcom/bytedance/sdk/component/ex/Fj/rAx$Fj;->Fj:Ljava/util/List;

    const-wide/16 v0, 0x2710

    iput-wide v0, p0, Lcom/bytedance/sdk/component/ex/Fj/rAx$Fj;->ex:J

    sget-object p1, Ljava/util/concurrent/TimeUnit;->MILLISECONDS:Ljava/util/concurrent/TimeUnit;

    iput-object p1, p0, Lcom/bytedance/sdk/component/ex/Fj/rAx$Fj;->hjc:Ljava/util/concurrent/TimeUnit;

    iput-wide v0, p0, Lcom/bytedance/sdk/component/ex/Fj/rAx$Fj;->eV:J

    iput-object p1, p0, Lcom/bytedance/sdk/component/ex/Fj/rAx$Fj;->Ubf:Ljava/util/concurrent/TimeUnit;

    iput-wide v0, p0, Lcom/bytedance/sdk/component/ex/Fj/rAx$Fj;->WR:J

    iput-object p1, p0, Lcom/bytedance/sdk/component/ex/Fj/rAx$Fj;->svN:Ljava/util/concurrent/TimeUnit;

    return-void
.end method


# virtual methods
.method public Fj(JLjava/util/concurrent/TimeUnit;)Lcom/bytedance/sdk/component/ex/Fj/rAx$Fj;
    .locals 0

    iput-wide p1, p0, Lcom/bytedance/sdk/component/ex/Fj/rAx$Fj;->ex:J

    iput-object p3, p0, Lcom/bytedance/sdk/component/ex/Fj/rAx$Fj;->hjc:Ljava/util/concurrent/TimeUnit;

    return-object p0
.end method

.method public Fj(Lcom/bytedance/sdk/component/ex/Fj/BcC;)Lcom/bytedance/sdk/component/ex/Fj/rAx$Fj;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/ex/Fj/rAx$Fj;->Fj:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-object p0
.end method

.method public Fj()Lcom/bytedance/sdk/component/ex/Fj/rAx;
    .locals 1

    invoke-static {p0}, Lcom/bytedance/sdk/component/ex/Fj/Fj/Fj;->Fj(Lcom/bytedance/sdk/component/ex/Fj/rAx$Fj;)Lcom/bytedance/sdk/component/ex/Fj/rAx;

    move-result-object v0

    return-object v0
.end method

.method public ex(JLjava/util/concurrent/TimeUnit;)Lcom/bytedance/sdk/component/ex/Fj/rAx$Fj;
    .locals 0

    iput-wide p1, p0, Lcom/bytedance/sdk/component/ex/Fj/rAx$Fj;->eV:J

    iput-object p3, p0, Lcom/bytedance/sdk/component/ex/Fj/rAx$Fj;->Ubf:Ljava/util/concurrent/TimeUnit;

    return-object p0
.end method

.method public hjc(JLjava/util/concurrent/TimeUnit;)Lcom/bytedance/sdk/component/ex/Fj/rAx$Fj;
    .locals 0

    iput-wide p1, p0, Lcom/bytedance/sdk/component/ex/Fj/rAx$Fj;->WR:J

    iput-object p3, p0, Lcom/bytedance/sdk/component/ex/Fj/rAx$Fj;->svN:Ljava/util/concurrent/TimeUnit;

    return-object p0
.end method
