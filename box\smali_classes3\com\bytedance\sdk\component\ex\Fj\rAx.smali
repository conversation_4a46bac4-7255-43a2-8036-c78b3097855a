.class public abstract Lcom/bytedance/sdk/component/ex/Fj/rAx;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/lang/Cloneable;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/bytedance/sdk/component/ex/Fj/rAx$Fj;
    }
.end annotation


# instance fields
.field public Fj:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lcom/bytedance/sdk/component/ex/Fj/BcC;",
            ">;"
        }
    .end annotation
.end field

.field public Ubf:Ljava/util/concurrent/TimeUnit;

.field public WR:J

.field public eV:J

.field public ex:J

.field public hjc:Ljava/util/concurrent/TimeUnit;

.field public svN:Ljava/util/concurrent/TimeUnit;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/component/ex/Fj/rAx$Fj;)V
    .locals 2

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iget-wide v0, p1, Lcom/bytedance/sdk/component/ex/Fj/rAx$Fj;->ex:J

    iput-wide v0, p0, Lcom/bytedance/sdk/component/ex/Fj/rAx;->ex:J

    iget-wide v0, p1, Lcom/bytedance/sdk/component/ex/Fj/rAx$Fj;->eV:J

    iput-wide v0, p0, Lcom/bytedance/sdk/component/ex/Fj/rAx;->eV:J

    iget-wide v0, p1, Lcom/bytedance/sdk/component/ex/Fj/rAx$Fj;->WR:J

    iput-wide v0, p0, Lcom/bytedance/sdk/component/ex/Fj/rAx;->WR:J

    iget-object v0, p1, Lcom/bytedance/sdk/component/ex/Fj/rAx$Fj;->Fj:Ljava/util/List;

    iget-object v1, p1, Lcom/bytedance/sdk/component/ex/Fj/rAx$Fj;->hjc:Ljava/util/concurrent/TimeUnit;

    iput-object v1, p0, Lcom/bytedance/sdk/component/ex/Fj/rAx;->hjc:Ljava/util/concurrent/TimeUnit;

    iget-object v1, p1, Lcom/bytedance/sdk/component/ex/Fj/rAx$Fj;->Ubf:Ljava/util/concurrent/TimeUnit;

    iput-object v1, p0, Lcom/bytedance/sdk/component/ex/Fj/rAx;->Ubf:Ljava/util/concurrent/TimeUnit;

    iget-object p1, p1, Lcom/bytedance/sdk/component/ex/Fj/rAx$Fj;->svN:Ljava/util/concurrent/TimeUnit;

    iput-object p1, p0, Lcom/bytedance/sdk/component/ex/Fj/rAx;->svN:Ljava/util/concurrent/TimeUnit;

    iput-object v0, p0, Lcom/bytedance/sdk/component/ex/Fj/rAx;->Fj:Ljava/util/List;

    return-void
.end method


# virtual methods
.method public abstract Fj()Lcom/bytedance/sdk/component/ex/Fj/eV;
.end method

.method public abstract Fj(Lcom/bytedance/sdk/component/ex/Fj/dG;)Lcom/bytedance/sdk/component/ex/Fj/ex;
.end method

.method public ex()Lcom/bytedance/sdk/component/ex/Fj/rAx$Fj;
    .locals 1

    new-instance v0, Lcom/bytedance/sdk/component/ex/Fj/rAx$Fj;

    invoke-direct {v0, p0}, Lcom/bytedance/sdk/component/ex/Fj/rAx$Fj;-><init>(Lcom/bytedance/sdk/component/ex/Fj/rAx;)V

    return-object v0
.end method
