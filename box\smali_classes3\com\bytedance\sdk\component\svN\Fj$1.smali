.class Lcom/bytedance/sdk/component/svN/Fj$1;
.super Lcom/bytedance/sdk/component/svN/BcC;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/component/svN/Fj;->execute(Ljava/lang/Runnable;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Ljava/lang/Runnable;

.field final synthetic ex:Lcom/bytedance/sdk/component/svN/Fj;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/component/svN/Fj;Ljava/lang/String;Ljava/lang/Runnable;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/svN/Fj$1;->ex:Lcom/bytedance/sdk/component/svN/Fj;

    iput-object p3, p0, Lcom/bytedance/sdk/component/svN/Fj$1;->Fj:Ljava/lang/Runnable;

    invoke-direct {p0, p2}, Lcom/bytedance/sdk/component/svN/BcC;-><init>(Ljava/lang/String;)V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/svN/Fj$1;->Fj:Ljava/lang/Runnable;

    invoke-interface {v0}, Ljava/lang/Runnable;->run()V

    return-void
.end method
