.class Lcom/bytedance/sdk/component/svN/Fj/Fj$Fj;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bytedance/sdk/component/svN/Fj/Fj;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "Fj"
.end annotation


# static fields
.field private static final Fj:Lcom/bytedance/sdk/component/svN/Fj/Fj;


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lcom/bytedance/sdk/component/svN/Fj/Fj;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lcom/bytedance/sdk/component/svN/Fj/Fj;-><init>(Lcom/bytedance/sdk/component/svN/Fj/Fj$1;)V

    sput-object v0, Lcom/bytedance/sdk/component/svN/Fj/Fj$Fj;->Fj:Lcom/bytedance/sdk/component/svN/Fj/Fj;

    return-void
.end method

.method public static synthetic Fj()Lcom/bytedance/sdk/component/svN/Fj/Fj;
    .locals 1

    sget-object v0, Lcom/bytedance/sdk/component/svN/Fj/Fj$Fj;->Fj:Lcom/bytedance/sdk/component/svN/Fj/Fj;

    return-object v0
.end method
