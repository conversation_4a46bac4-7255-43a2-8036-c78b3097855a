.class public Lcom/bytedance/sdk/component/svN/Ubf;
.super Ljava/lang/Object;


# static fields
.field private static Fj:Lcom/bytedance/sdk/component/svN/Ko;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Lcom/bytedance/sdk/component/svN/Ubf$1;

    invoke-direct {v0}, Lcom/bytedance/sdk/component/svN/Ubf$1;-><init>()V

    sput-object v0, Lcom/bytedance/sdk/component/svN/Ubf;->Fj:Lcom/bytedance/sdk/component/svN/Ko;

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static Fj()Lcom/bytedance/sdk/component/svN/Ko;
    .locals 1

    sget-object v0, Lcom/bytedance/sdk/component/svN/Ubf;->Fj:Lcom/bytedance/sdk/component/svN/Ko;

    return-object v0
.end method

.method public static Fj(Lcom/bytedance/sdk/component/svN/Ko;)V
    .locals 0

    sput-object p0, Lcom/bytedance/sdk/component/svN/Ubf;->Fj:Lcom/bytedance/sdk/component/svN/Ko;

    return-void
.end method
