.class public abstract Lcom/bytedance/sdk/component/svN/hjc/Fj;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/lang/Comparable;
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Ljava/lang/Comparable<",
        "Lcom/bytedance/sdk/component/svN/hjc/Fj;",
        ">;",
        "Ljava/lang/Runnable;"
    }
.end annotation


# instance fields
.field private Fj:I

.field private Ubf:J

.field private WR:J

.field private eV:J

.field private ex:Ljava/lang/String;

.field private hjc:Ljava/lang/Runnable;


# direct methods
.method public constructor <init>(Ljava/lang/String;)V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, 0x5

    iput v0, p0, Lcom/bytedance/sdk/component/svN/hjc/Fj;->Fj:I

    iput-object p1, p0, Lcom/bytedance/sdk/component/svN/hjc/Fj;->ex:Ljava/lang/String;

    return-void
.end method

.method public constructor <init>(Ljava/lang/String;Ljava/lang/Runnable;)V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, 0x5

    iput v0, p0, Lcom/bytedance/sdk/component/svN/hjc/Fj;->Fj:I

    iput-object p1, p0, Lcom/bytedance/sdk/component/svN/hjc/Fj;->ex:Ljava/lang/String;

    iput-object p2, p0, Lcom/bytedance/sdk/component/svN/hjc/Fj;->hjc:Ljava/lang/Runnable;

    return-void
.end method


# virtual methods
.method public Fj()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/component/svN/hjc/Fj;->Fj:I

    return v0
.end method

.method public Fj(Lcom/bytedance/sdk/component/svN/hjc/Fj;)I
    .locals 2

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/svN/hjc/Fj;->Fj()I

    move-result v0

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/svN/hjc/Fj;->Fj()I

    move-result v1

    if-ge v0, v1, :cond_0

    const/4 p1, 0x1

    return p1

    :cond_0
    invoke-virtual {p0}, Lcom/bytedance/sdk/component/svN/hjc/Fj;->Fj()I

    move-result v0

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/svN/hjc/Fj;->Fj()I

    move-result p1

    if-lt v0, p1, :cond_1

    const/4 p1, -0x1

    return p1

    :cond_1
    const/4 p1, 0x0

    return p1
.end method

.method public Fj(I)V
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/component/svN/hjc/Fj;->Fj:I

    return-void
.end method

.method public Fj(J)V
    .locals 0

    iput-wide p1, p0, Lcom/bytedance/sdk/component/svN/hjc/Fj;->eV:J

    return-void
.end method

.method public Ubf()Ljava/lang/Runnable;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/svN/hjc/Fj;->hjc:Ljava/lang/Runnable;

    return-object v0
.end method

.method public synthetic compareTo(Ljava/lang/Object;)I
    .locals 0

    check-cast p1, Lcom/bytedance/sdk/component/svN/hjc/Fj;

    invoke-virtual {p0, p1}, Lcom/bytedance/sdk/component/svN/hjc/Fj;->Fj(Lcom/bytedance/sdk/component/svN/hjc/Fj;)I

    move-result p1

    return p1
.end method

.method public eV()J
    .locals 4

    iget-wide v0, p0, Lcom/bytedance/sdk/component/svN/hjc/Fj;->WR:J

    iget-wide v2, p0, Lcom/bytedance/sdk/component/svN/hjc/Fj;->Ubf:J

    sub-long/2addr v0, v2

    return-wide v0
.end method

.method public ex()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/svN/hjc/Fj;->ex:Ljava/lang/String;

    return-object v0
.end method

.method public ex(J)V
    .locals 0

    iput-wide p1, p0, Lcom/bytedance/sdk/component/svN/hjc/Fj;->Ubf:J

    return-void
.end method

.method public hjc()J
    .locals 4

    iget-wide v0, p0, Lcom/bytedance/sdk/component/svN/hjc/Fj;->Ubf:J

    iget-wide v2, p0, Lcom/bytedance/sdk/component/svN/hjc/Fj;->eV:J

    sub-long/2addr v0, v2

    return-wide v0
.end method

.method public hjc(J)V
    .locals 0

    iput-wide p1, p0, Lcom/bytedance/sdk/component/svN/hjc/Fj;->WR:J

    return-void
.end method
