.class Lcom/bytedance/sdk/component/svN/hjc/Ubf$1;
.super Ljava/util/LinkedHashMap;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/component/svN/hjc/Ubf;-><init>(Lcom/bytedance/sdk/component/svN/hjc/Ubf$Fj;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/util/LinkedHashMap<",
        "Ljava/lang/String;",
        "Lcom/bytedance/sdk/component/svN/hjc/Fj/Fj;",
        ">;"
    }
.end annotation


# instance fields
.field final synthetic Fj:I

.field final synthetic ex:Lcom/bytedance/sdk/component/svN/hjc/Ubf;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/component/svN/hjc/Ubf;IFZI)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/svN/hjc/Ubf$1;->ex:Lcom/bytedance/sdk/component/svN/hjc/Ubf;

    iput p5, p0, Lcom/bytedance/sdk/component/svN/hjc/Ubf$1;->Fj:I

    invoke-direct {p0, p2, p3, p4}, Ljava/util/LinkedHashMap;-><init>(IFZ)V

    return-void
.end method


# virtual methods
.method public removeEldestEntry(Ljava/util/Map$Entry;)Z
    .locals 1

    invoke-virtual {p0}, Ljava/util/AbstractMap;->size()I

    move-result p1

    iget v0, p0, Lcom/bytedance/sdk/component/svN/hjc/Ubf$1;->Fj:I

    if-le p1, v0, :cond_0

    const/4 p1, 0x1

    return p1

    :cond_0
    const/4 p1, 0x0

    return p1
.end method
