.class Lcom/bytedance/sdk/component/svN/hjc/Ubf$2;
.super Lcom/bytedance/sdk/component/svN/hjc/Fj;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/component/svN/hjc/Ubf;->execute(Ljava/lang/Runnable;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/component/svN/hjc/Ubf;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/component/svN/hjc/Ubf;Ljava/lang/String;Ljava/lang/Runnable;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/svN/hjc/Ubf$2;->Fj:Lcom/bytedance/sdk/component/svN/hjc/Ubf;

    invoke-direct {p0, p2, p3}, Lcom/bytedance/sdk/component/svN/hjc/Fj;-><init>(Ljava/lang/String;Ljava/lang/Runnable;)V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 1

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/svN/hjc/Fj;->Ubf()Ljava/lang/Runnable;

    move-result-object v0

    if-eqz v0, :cond_0

    invoke-interface {v0}, Ljava/lang/Runnable;->run()V

    :cond_0
    return-void
.end method
