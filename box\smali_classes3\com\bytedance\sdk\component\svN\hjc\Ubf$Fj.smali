.class public Lcom/bytedance/sdk/component/svN/hjc/Ubf$Fj;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bytedance/sdk/component/svN/hjc/Ubf;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "Fj"
.end annotation


# instance fields
.field private BcC:I

.field private Fj:Ljava/lang/String;

.field private Ko:Z

.field private UYd:Ljava/util/concurrent/ThreadFactory;

.field private Ubf:J

.field private WR:Z

.field private eV:I

.field private ex:I

.field private hjc:I

.field private mSE:I

.field private rAx:Ljava/util/concurrent/BlockingQueue;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/concurrent/BlockingQueue<",
            "Ljava/lang/Runnable;",
            ">;"
        }
    .end annotation
.end field

.field private svN:Ljava/util/concurrent/TimeUnit;


# direct methods
.method public constructor <init>()V
    .locals 3

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const-string v0, "cache"

    iput-object v0, p0, Lcom/bytedance/sdk/component/svN/hjc/Ubf$Fj;->Fj:Ljava/lang/String;

    const/4 v0, 0x4

    iput v0, p0, Lcom/bytedance/sdk/component/svN/hjc/Ubf$Fj;->ex:I

    const/16 v0, 0x64

    iput v0, p0, Lcom/bytedance/sdk/component/svN/hjc/Ubf$Fj;->hjc:I

    const/4 v0, 0x0

    iput v0, p0, Lcom/bytedance/sdk/component/svN/hjc/Ubf$Fj;->eV:I

    const-wide/16 v1, 0x7530

    iput-wide v1, p0, Lcom/bytedance/sdk/component/svN/hjc/Ubf$Fj;->Ubf:J

    iput-boolean v0, p0, Lcom/bytedance/sdk/component/svN/hjc/Ubf$Fj;->WR:Z

    sget-object v1, Ljava/util/concurrent/TimeUnit;->MILLISECONDS:Ljava/util/concurrent/TimeUnit;

    iput-object v1, p0, Lcom/bytedance/sdk/component/svN/hjc/Ubf$Fj;->svN:Ljava/util/concurrent/TimeUnit;

    const/4 v1, -0x1

    iput v1, p0, Lcom/bytedance/sdk/component/svN/hjc/Ubf$Fj;->BcC:I

    const/16 v1, 0x14

    iput v1, p0, Lcom/bytedance/sdk/component/svN/hjc/Ubf$Fj;->mSE:I

    iput-boolean v0, p0, Lcom/bytedance/sdk/component/svN/hjc/Ubf$Fj;->Ko:Z

    new-instance v0, Ljava/util/concurrent/PriorityBlockingQueue;

    invoke-direct {v0}, Ljava/util/concurrent/PriorityBlockingQueue;-><init>()V

    iput-object v0, p0, Lcom/bytedance/sdk/component/svN/hjc/Ubf$Fj;->rAx:Ljava/util/concurrent/BlockingQueue;

    const/4 v0, 0x0

    iput-object v0, p0, Lcom/bytedance/sdk/component/svN/hjc/Ubf$Fj;->UYd:Ljava/util/concurrent/ThreadFactory;

    return-void
.end method

.method public static synthetic BcC(Lcom/bytedance/sdk/component/svN/hjc/Ubf$Fj;)I
    .locals 0

    iget p0, p0, Lcom/bytedance/sdk/component/svN/hjc/Ubf$Fj;->eV:I

    return p0
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/component/svN/hjc/Ubf$Fj;)I
    .locals 0

    iget p0, p0, Lcom/bytedance/sdk/component/svN/hjc/Ubf$Fj;->ex:I

    return p0
.end method

.method public static synthetic Ko(Lcom/bytedance/sdk/component/svN/hjc/Ubf$Fj;)I
    .locals 0

    iget p0, p0, Lcom/bytedance/sdk/component/svN/hjc/Ubf$Fj;->BcC:I

    return p0
.end method

.method public static synthetic UYd(Lcom/bytedance/sdk/component/svN/hjc/Ubf$Fj;)Z
    .locals 0

    iget-boolean p0, p0, Lcom/bytedance/sdk/component/svN/hjc/Ubf$Fj;->Ko:Z

    return p0
.end method

.method public static synthetic Ubf(Lcom/bytedance/sdk/component/svN/hjc/Ubf$Fj;)Ljava/util/concurrent/ThreadFactory;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/component/svN/hjc/Ubf$Fj;->UYd:Ljava/util/concurrent/ThreadFactory;

    return-object p0
.end method

.method public static synthetic WR(Lcom/bytedance/sdk/component/svN/hjc/Ubf$Fj;)Ljava/lang/String;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/component/svN/hjc/Ubf$Fj;->Fj:Ljava/lang/String;

    return-object p0
.end method

.method public static synthetic eV(Lcom/bytedance/sdk/component/svN/hjc/Ubf$Fj;)Ljava/util/concurrent/BlockingQueue;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/component/svN/hjc/Ubf$Fj;->rAx:Ljava/util/concurrent/BlockingQueue;

    return-object p0
.end method

.method public static synthetic ex(Lcom/bytedance/sdk/component/svN/hjc/Ubf$Fj;)J
    .locals 2

    iget-wide v0, p0, Lcom/bytedance/sdk/component/svN/hjc/Ubf$Fj;->Ubf:J

    return-wide v0
.end method

.method public static synthetic hjc(Lcom/bytedance/sdk/component/svN/hjc/Ubf$Fj;)Ljava/util/concurrent/TimeUnit;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/component/svN/hjc/Ubf$Fj;->svN:Ljava/util/concurrent/TimeUnit;

    return-object p0
.end method

.method public static synthetic mSE(Lcom/bytedance/sdk/component/svN/hjc/Ubf$Fj;)Z
    .locals 0

    iget-boolean p0, p0, Lcom/bytedance/sdk/component/svN/hjc/Ubf$Fj;->WR:Z

    return p0
.end method

.method public static synthetic rAx(Lcom/bytedance/sdk/component/svN/hjc/Ubf$Fj;)I
    .locals 0

    iget p0, p0, Lcom/bytedance/sdk/component/svN/hjc/Ubf$Fj;->mSE:I

    return p0
.end method

.method public static synthetic svN(Lcom/bytedance/sdk/component/svN/hjc/Ubf$Fj;)I
    .locals 0

    iget p0, p0, Lcom/bytedance/sdk/component/svN/hjc/Ubf$Fj;->hjc:I

    return p0
.end method


# virtual methods
.method public Fj(I)Lcom/bytedance/sdk/component/svN/hjc/Ubf$Fj;
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/component/svN/hjc/Ubf$Fj;->ex:I

    return-object p0
.end method

.method public Fj(J)Lcom/bytedance/sdk/component/svN/hjc/Ubf$Fj;
    .locals 0

    iput-wide p1, p0, Lcom/bytedance/sdk/component/svN/hjc/Ubf$Fj;->Ubf:J

    return-object p0
.end method

.method public Fj(Ljava/lang/String;)Lcom/bytedance/sdk/component/svN/hjc/Ubf$Fj;
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/svN/hjc/Ubf$Fj;->Fj:Ljava/lang/String;

    return-object p0
.end method

.method public Fj(Z)Lcom/bytedance/sdk/component/svN/hjc/Ubf$Fj;
    .locals 0

    iput-boolean p1, p0, Lcom/bytedance/sdk/component/svN/hjc/Ubf$Fj;->WR:Z

    return-object p0
.end method

.method public Fj()Lcom/bytedance/sdk/component/svN/hjc/Ubf;
    .locals 3

    iget-object v0, p0, Lcom/bytedance/sdk/component/svN/hjc/Ubf$Fj;->UYd:Ljava/util/concurrent/ThreadFactory;

    if-nez v0, :cond_0

    new-instance v0, Lcom/bytedance/sdk/component/svN/hjc/hjc;

    iget-object v1, p0, Lcom/bytedance/sdk/component/svN/hjc/Ubf$Fj;->Fj:Ljava/lang/String;

    invoke-direct {v0, v1}, Lcom/bytedance/sdk/component/svN/hjc/hjc;-><init>(Ljava/lang/String;)V

    iput-object v0, p0, Lcom/bytedance/sdk/component/svN/hjc/Ubf$Fj;->UYd:Ljava/util/concurrent/ThreadFactory;

    :cond_0
    iget v0, p0, Lcom/bytedance/sdk/component/svN/hjc/Ubf$Fj;->ex:I

    if-gez v0, :cond_1

    const/16 v0, 0x8

    iput v0, p0, Lcom/bytedance/sdk/component/svN/hjc/Ubf$Fj;->ex:I

    :cond_1
    iget v0, p0, Lcom/bytedance/sdk/component/svN/hjc/Ubf$Fj;->ex:I

    if-nez v0, :cond_2

    new-instance v0, Ljava/util/concurrent/SynchronousQueue;

    invoke-direct {v0}, Ljava/util/concurrent/SynchronousQueue;-><init>()V

    iput-object v0, p0, Lcom/bytedance/sdk/component/svN/hjc/Ubf$Fj;->rAx:Ljava/util/concurrent/BlockingQueue;

    :cond_2
    iget-object v0, p0, Lcom/bytedance/sdk/component/svN/hjc/Ubf$Fj;->rAx:Ljava/util/concurrent/BlockingQueue;

    if-nez v0, :cond_3

    new-instance v0, Ljava/util/concurrent/LinkedBlockingQueue;

    invoke-direct {v0}, Ljava/util/concurrent/LinkedBlockingQueue;-><init>()V

    iput-object v0, p0, Lcom/bytedance/sdk/component/svN/hjc/Ubf$Fj;->rAx:Ljava/util/concurrent/BlockingQueue;

    :cond_3
    iget v0, p0, Lcom/bytedance/sdk/component/svN/hjc/Ubf$Fj;->hjc:I

    const/16 v1, 0x64

    if-le v0, v1, :cond_4

    iput v1, p0, Lcom/bytedance/sdk/component/svN/hjc/Ubf$Fj;->hjc:I

    :cond_4
    iget v0, p0, Lcom/bytedance/sdk/component/svN/hjc/Ubf$Fj;->hjc:I

    iget v2, p0, Lcom/bytedance/sdk/component/svN/hjc/Ubf$Fj;->ex:I

    if-ge v0, v2, :cond_5

    iput v2, p0, Lcom/bytedance/sdk/component/svN/hjc/Ubf$Fj;->hjc:I

    :cond_5
    iget v0, p0, Lcom/bytedance/sdk/component/svN/hjc/Ubf$Fj;->mSE:I

    if-gez v0, :cond_6

    const/16 v0, 0x14

    iput v0, p0, Lcom/bytedance/sdk/component/svN/hjc/Ubf$Fj;->mSE:I

    :cond_6
    iget v0, p0, Lcom/bytedance/sdk/component/svN/hjc/Ubf$Fj;->mSE:I

    if-le v0, v1, :cond_7

    iput v1, p0, Lcom/bytedance/sdk/component/svN/hjc/Ubf$Fj;->mSE:I

    :cond_7
    new-instance v0, Lcom/bytedance/sdk/component/svN/hjc/Ubf;

    const/4 v1, 0x0

    invoke-direct {v0, p0, v1}, Lcom/bytedance/sdk/component/svN/hjc/Ubf;-><init>(Lcom/bytedance/sdk/component/svN/hjc/Ubf$Fj;Lcom/bytedance/sdk/component/svN/hjc/Ubf$1;)V

    return-object v0
.end method

.method public Ubf(I)Lcom/bytedance/sdk/component/svN/hjc/Ubf$Fj;
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/component/svN/hjc/Ubf$Fj;->BcC:I

    return-object p0
.end method

.method public eV(I)Lcom/bytedance/sdk/component/svN/hjc/Ubf$Fj;
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/component/svN/hjc/Ubf$Fj;->mSE:I

    return-object p0
.end method

.method public ex(I)Lcom/bytedance/sdk/component/svN/hjc/Ubf$Fj;
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/component/svN/hjc/Ubf$Fj;->hjc:I

    return-object p0
.end method

.method public ex(Z)Lcom/bytedance/sdk/component/svN/hjc/Ubf$Fj;
    .locals 0

    iput-boolean p1, p0, Lcom/bytedance/sdk/component/svN/hjc/Ubf$Fj;->Ko:Z

    return-object p0
.end method

.method public hjc(I)Lcom/bytedance/sdk/component/svN/hjc/Ubf$Fj;
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/component/svN/hjc/Ubf$Fj;->eV:I

    return-object p0
.end method
