.class public Lcom/bytedance/sdk/component/svN/hjc/ex;
.super Ljava/lang/Object;


# static fields
.field public static Fj:Lcom/bytedance/sdk/component/svN/hjc/eV;


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public static Fj()Lcom/bytedance/sdk/component/svN/hjc/eV;
    .locals 1

    sget-object v0, Lcom/bytedance/sdk/component/svN/hjc/ex;->Fj:Lcom/bytedance/sdk/component/svN/hjc/eV;

    return-object v0
.end method

.method public static Fj(Lcom/bytedance/sdk/component/svN/hjc/eV;)V
    .locals 0

    sput-object p0, Lcom/bytedance/sdk/component/svN/hjc/ex;->Fj:Lcom/bytedance/sdk/component/svN/hjc/eV;

    return-void
.end method
