.class public interface abstract Lcom/bytedance/sdk/component/utils/HomeWatcherReceiver$Fj;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bytedance/sdk/component/utils/HomeWatcherReceiver;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "Fj"
.end annotation


# virtual methods
.method public abstract Fj()V
.end method

.method public abstract ex()V
.end method
