.class public Lcom/bytedance/sdk/component/utils/UYd;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/bytedance/sdk/component/utils/UYd$ex;,
        Lcom/bytedance/sdk/component/utils/UYd$Fj;
    }
.end annotation


# static fields
.field private static final Fj:Lcom/bytedance/sdk/component/utils/UYd$Fj;


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lcom/bytedance/sdk/component/utils/UYd$ex;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lcom/bytedance/sdk/component/utils/UYd$ex;-><init>(Lcom/bytedance/sdk/component/utils/UYd$1;)V

    sput-object v0, Lcom/bytedance/sdk/component/utils/UYd;->Fj:Lcom/bytedance/sdk/component/utils/UYd$Fj;

    return-void
.end method

.method public static Fj(Landroid/webkit/WebView;Ljava/lang/String;)V
    .locals 1

    sget-object v0, Lcom/bytedance/sdk/component/utils/UYd;->Fj:Lcom/bytedance/sdk/component/utils/UYd$Fj;

    invoke-virtual {v0, p0, p1}, Lcom/bytedance/sdk/component/utils/UYd$Fj;->Fj(Landroid/webkit/WebView;Ljava/lang/String;)V

    return-void
.end method
