.class public Lcom/bytedance/sdk/component/widget/Fj/Fj;
.super Ljava/lang/Object;


# static fields
.field private static volatile ex:Lcom/bytedance/sdk/component/widget/Fj/Fj;


# instance fields
.field private volatile Fj:Lcom/bytedance/sdk/component/widget/Fj/ex;


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static Fj()Lcom/bytedance/sdk/component/widget/Fj/Fj;
    .locals 2

    sget-object v0, Lcom/bytedance/sdk/component/widget/Fj/Fj;->ex:Lcom/bytedance/sdk/component/widget/Fj/Fj;

    if-nez v0, :cond_1

    const-class v0, Lcom/bytedance/sdk/component/widget/Fj/Fj;

    monitor-enter v0

    :try_start_0
    sget-object v1, Lcom/bytedance/sdk/component/widget/Fj/Fj;->ex:Lcom/bytedance/sdk/component/widget/Fj/Fj;

    if-nez v1, :cond_0

    new-instance v1, Lcom/bytedance/sdk/component/widget/Fj/Fj;

    invoke-direct {v1}, Lcom/bytedance/sdk/component/widget/Fj/Fj;-><init>()V

    sput-object v1, Lcom/bytedance/sdk/component/widget/Fj/Fj;->ex:Lcom/bytedance/sdk/component/widget/Fj/Fj;

    goto :goto_0

    :catchall_0
    move-exception v1

    goto :goto_1

    :cond_0
    :goto_0
    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    goto :goto_2

    :goto_1
    monitor-exit v0

    throw v1

    :cond_1
    :goto_2
    sget-object v0, Lcom/bytedance/sdk/component/widget/Fj/Fj;->ex:Lcom/bytedance/sdk/component/widget/Fj/Fj;

    return-object v0
.end method


# virtual methods
.method public Fj(Lcom/bytedance/sdk/component/widget/Fj/ex;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/widget/Fj/Fj;->Fj:Lcom/bytedance/sdk/component/widget/Fj/ex;

    return-void
.end method

.method public ex()Lcom/bytedance/sdk/component/widget/Fj/ex;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/widget/Fj/Fj;->Fj:Lcom/bytedance/sdk/component/widget/Fj/ex;

    return-object v0
.end method
