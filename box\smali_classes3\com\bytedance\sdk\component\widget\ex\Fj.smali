.class public Lcom/bytedance/sdk/component/widget/ex/Fj;
.super Ljava/lang/Object;


# instance fields
.field public Fj:I

.field public ex:Ljava/lang/String;

.field public hjc:Ljava/lang/String;


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public Fj()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/component/widget/ex/Fj;->Fj:I

    return v0
.end method

.method public Fj(I)V
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/component/widget/ex/Fj;->Fj:I

    return-void
.end method

.method public Fj(Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/widget/ex/Fj;->ex:Ljava/lang/String;

    return-void
.end method

.method public ex()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/widget/ex/Fj;->ex:Ljava/lang/String;

    return-object v0
.end method

.method public ex(Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/widget/ex/Fj;->hjc:Ljava/lang/String;

    return-void
.end method

.method public hjc()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/widget/ex/Fj;->hjc:Ljava/lang/String;

    return-object v0
.end method
