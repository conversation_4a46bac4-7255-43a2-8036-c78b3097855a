.class public Lcom/bytedance/sdk/openadsdk/activity/TTRewardExpressVideoActivity;
.super Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity;


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity;-><init>()V

    return-void
.end method


# virtual methods
.method public Fj(JZ)Z
    .locals 9

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->spi:Lcom/bytedance/sdk/openadsdk/component/reward/view/hjc;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/view/hjc;->Fj()Lcom/bytedance/sdk/openadsdk/component/reward/view/FullRewardExpressView;

    move-result-object v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->spi:Lcom/bytedance/sdk/openadsdk/component/reward/view/hjc;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/view/hjc;->Fj()Lcom/bytedance/sdk/openadsdk/component/reward/view/FullRewardExpressView;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/NativeExpressView;->getAdShowTime()Lcom/bytedance/sdk/openadsdk/ex/svN;

    move-result-object v0

    goto :goto_0

    :cond_0
    new-instance v0, Lcom/bytedance/sdk/openadsdk/ex/svN;

    invoke-direct {v0}, Lcom/bytedance/sdk/openadsdk/ex/svN;-><init>()V

    :goto_0
    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object v2, v1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->kF:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;

    iget-object v1, v1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->spi:Lcom/bytedance/sdk/openadsdk/component/reward/view/hjc;

    invoke-virtual {v1}, Lcom/bytedance/sdk/openadsdk/component/reward/view/hjc;->ex()Landroid/widget/FrameLayout;

    move-result-object v1

    invoke-virtual {v2, v1, v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->Fj(Landroid/widget/FrameLayout;Lcom/bytedance/sdk/openadsdk/ex/svN;)V

    new-instance v7, Ljava/util/HashMap;

    invoke-direct {v7}, Ljava/util/HashMap;-><init>()V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->spi:Lcom/bytedance/sdk/openadsdk/component/reward/view/hjc;

    if-eqz v0, :cond_1

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/view/hjc;->mSE()I

    move-result v0

    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v0

    const-string v1, "dynamic_show_type"

    invoke-interface {v7, v1, v0}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->spi:Lcom/bytedance/sdk/openadsdk/component/reward/view/hjc;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/openadsdk/component/reward/view/hjc;->Fj(Lorg/json/JSONObject;)Lorg/json/JSONObject;

    move-result-object v0

    if-eqz v0, :cond_1

    invoke-virtual {v0}, Lorg/json/JSONObject;->keys()Ljava/util/Iterator;

    move-result-object v1

    :goto_1
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_1

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Ljava/lang/String;

    :try_start_0
    invoke-virtual {v0, v2}, Lorg/json/JSONObject;->get(Ljava/lang/String;)Ljava/lang/Object;

    move-result-object v3

    invoke-interface {v7, v2, v3}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    :try_end_0
    .catch Lorg/json/JSONException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_1

    :catch_0
    nop

    goto :goto_1

    :cond_1
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->kF:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;

    new-instance v1, Lcom/bytedance/sdk/openadsdk/activity/TTRewardExpressVideoActivity$1;

    invoke-direct {v1, p0}, Lcom/bytedance/sdk/openadsdk/activity/TTRewardExpressVideoActivity$1;-><init>(Lcom/bytedance/sdk/openadsdk/activity/TTRewardExpressVideoActivity;)V

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->Fj(Lcom/bykv/vk/openvk/component/video/api/eV/hjc$Fj;)V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object v3, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->kF:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;

    iget-object v8, p0, Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;->hjc:Lcom/bytedance/sdk/openadsdk/component/reward/ex/ex;

    move-wide v4, p1

    move v6, p3

    invoke-virtual/range {v3 .. v8}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->Fj(JZLjava/util/Map;Lcom/bytedance/sdk/openadsdk/component/reward/ex/ex;)Z

    move-result p1

    if-eqz p1, :cond_2

    if-nez p3, :cond_2

    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide p2

    const-wide/16 v0, 0x3e8

    div-long/2addr p2, v0

    long-to-int p3, p2

    iput p3, p0, Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity;->UYd:I

    :cond_2
    return p1
.end method

.method public JU()Z
    .locals 1

    const/4 v0, 0x1

    return v0
.end method

.method public Ql()Z
    .locals 1

    const/4 v0, 0x1

    return v0
.end method

.method public WR()V
    .locals 0

    return-void
.end method
