.class Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity$6;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bykv/vk/openvk/component/video/api/eV/hjc$Fj;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity;->Fj(JZ)Z
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field Fj:Z

.field final synthetic ex:Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity$6;->ex:Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public Fj()V
    .locals 4

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity$6;->ex:Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;->eV:Lcom/bytedance/sdk/component/utils/Vq;

    const/16 v1, 0x12c

    invoke-virtual {v0, v1}, Landroid/os/Handler;->removeMessages(I)V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity$6;->ex:Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;->dG()V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity$6;->ex:Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity;->mE()V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity$6;->ex:Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity;

    iget-object v1, v0, Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-boolean v1, v1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->ex:Z

    const/4 v2, 0x1

    if-eqz v1, :cond_0

    const/4 v1, 0x0

    const/4 v3, 0x6

    invoke-virtual {v0, v1, v2, v3}, Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;->Fj(ZZI)V

    goto :goto_0

    :cond_0
    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity;->finish()V

    :goto_0
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity$6;->ex:Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->kF:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->Moo()Z

    move-result v1

    xor-int/2addr v1, v2

    iget-object v3, p0, Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity$6;->ex:Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity;

    iget-object v3, v3, Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object v3, v3, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->kF:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;

    invoke-virtual {v3}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->Moo()Z

    move-result v3

    xor-int/2addr v2, v3

    invoke-virtual {v0, v1, v2}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->Fj(II)V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity$6;->ex:Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->kF:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->dG()V

    return-void
.end method

.method public Fj(JI)V
    .locals 4

    iget-boolean p1, p0, Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity$6;->Fj:Z

    if-eqz p1, :cond_0

    return-void

    :cond_0
    const/4 p1, 0x1

    iput-boolean p1, p0, Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity$6;->Fj:Z

    iget-object p2, p0, Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity$6;->ex:Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity;

    iget-object p2, p2, Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;->eV:Lcom/bytedance/sdk/component/utils/Vq;

    const/16 p3, 0x12c

    invoke-virtual {p2, p3}, Landroid/os/Handler;->removeMessages(I)V

    iget-object p2, p0, Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity$6;->ex:Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity;

    invoke-virtual {p2}, Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;->dG()V

    iget-object p2, p0, Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity$6;->ex:Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity;

    invoke-virtual {p2}, Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity;->mE()V

    iget-object p2, p0, Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity$6;->ex:Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity;

    iget-object p2, p2, Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object p2, p2, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->rXP:Ljava/util/concurrent/atomic/AtomicBoolean;

    invoke-virtual {p2, p1}, Ljava/util/concurrent/atomic/AtomicBoolean;->set(Z)V

    iget-object p2, p0, Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity$6;->ex:Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity;

    iget-object p2, p2, Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object p2, p2, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {p2}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->uJB()Z

    move-result p2

    if-eqz p2, :cond_1

    iget-object p2, p0, Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity$6;->ex:Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity;

    iget-object p2, p2, Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object p2, p2, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {p2, p1}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->fj(I)V

    iget-object p2, p0, Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity$6;->ex:Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity;

    iget-object p2, p2, Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object p2, p2, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Obv:Lcom/bytedance/sdk/openadsdk/component/reward/view/Ubf;

    invoke-virtual {p2}, Lcom/bytedance/sdk/openadsdk/component/reward/view/Ubf;->Ql()V

    :cond_1
    iget-object p2, p0, Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity$6;->ex:Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity;

    iget-object p2, p2, Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object p2, p2, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {p2}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->Tc()I

    move-result p2

    const/16 p3, 0x15

    if-ne p2, p3, :cond_2

    iget-object p2, p0, Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity$6;->ex:Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity;

    iget-object p2, p2, Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object p2, p2, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {p2}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->hjc()Z

    move-result p2

    if-nez p2, :cond_2

    iget-object p2, p0, Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity$6;->ex:Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity;

    iget-object p2, p2, Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object p2, p2, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {p2, p1}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->ex(Z)V

    iget-object p2, p0, Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity$6;->ex:Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity;

    iget-object p2, p2, Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object p2, p2, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Obv:Lcom/bytedance/sdk/openadsdk/component/reward/view/Ubf;

    invoke-virtual {p2}, Lcom/bytedance/sdk/openadsdk/component/reward/view/Ubf;->Ql()V

    :cond_2
    iget-object p2, p0, Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity$6;->ex:Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity;

    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v0

    const-wide/16 v2, 0x3e8

    div-long/2addr v0, v2

    long-to-int p3, v0

    iput p3, p2, Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity;->dG:I

    iget-object p2, p0, Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity$6;->ex:Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity;

    iget-object p2, p2, Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object p2, p2, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-static {p2}, Lcom/bytedance/sdk/openadsdk/core/model/JW;->hjc(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Z

    move-result p2

    const/4 p3, 0x5

    const/4 v0, 0x0

    if-nez p2, :cond_6

    iget-object p2, p0, Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity$6;->ex:Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity;

    iget-object p2, p2, Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object p2, p2, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-static {p2}, Lcom/bytedance/sdk/openadsdk/core/model/JW;->ex(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Z

    move-result p2

    if-eqz p2, :cond_3

    goto :goto_0

    :cond_3
    iget-object p2, p0, Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity$6;->ex:Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity;

    iget-object p2, p2, Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object p2, p2, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-static {p2}, Lcom/bytedance/sdk/openadsdk/core/model/JW;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Z

    move-result p2

    if-eqz p2, :cond_4

    iget-object p2, p0, Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity$6;->ex:Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity;

    iget-object p2, p2, Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object p2, p2, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->rf:Ljava/util/concurrent/atomic/AtomicBoolean;

    invoke-virtual {p2}, Ljava/util/concurrent/atomic/AtomicBoolean;->get()Z

    move-result p2

    if-nez p2, :cond_4

    iget-object p2, p0, Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity$6;->ex:Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity;

    iget-object p2, p2, Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    invoke-virtual {p2, p1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Fj(Z)V

    iget-object p2, p0, Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity$6;->ex:Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity;

    iget-object p2, p2, Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object p2, p2, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->gci:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Ko;

    invoke-virtual {p2, p1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Ko;->Ubf(Z)V

    return-void

    :cond_4
    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity$6;->ex:Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity;

    iget-object p2, p1, Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-boolean p2, p2, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->ex:Z

    if-eqz p2, :cond_5

    invoke-virtual {p1, v0, p3}, Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;->Fj(ZI)V

    return-void

    :cond_5
    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity;->finish()V

    return-void

    :cond_6
    :goto_0
    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity$6;->ex:Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity;

    invoke-virtual {p1, v0, p3}, Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;->Fj(ZI)V

    return-void
.end method

.method public Fj(JJ)V
    .locals 9

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity$6;->ex:Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-boolean v1, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Ubf:Z

    if-nez v1, :cond_0

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->kF:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->ex()Z

    move-result v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity$6;->ex:Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->kF:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->JW()V

    :cond_0
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity$6;->ex:Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Af:Ljava/util/concurrent/atomic/AtomicBoolean;

    invoke-virtual {v0}, Ljava/util/concurrent/atomic/AtomicBoolean;->get()Z

    move-result v0

    if-eqz v0, :cond_1

    return-void

    :cond_1
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity$6;->ex:Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;->eV:Lcom/bytedance/sdk/component/utils/Vq;

    const/16 v1, 0x12c

    invoke-virtual {v0, v1}, Landroid/os/Handler;->removeMessages(I)V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity$6;->ex:Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->kF:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->BcC()J

    move-result-wide v0

    cmp-long v2, p1, v0

    if-eqz v2, :cond_2

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity$6;->ex:Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;->dG()V

    :cond_2
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity$6;->ex:Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->kF:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;

    invoke-virtual {v0, p1, p2, p3, p4}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->Fj(JJ)V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity$6;->ex:Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity;

    iget-object v1, v0, Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object v1, v1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->kF:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;

    invoke-virtual {v1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->rf()D

    move-result-wide v1

    const-wide/16 v3, 0x3e8

    div-long v3, p1, v3

    long-to-double v5, v3

    sub-double/2addr v1, v5

    double-to-int v1, v1

    iput v1, v0, Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;->WR:I

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity$6;->ex:Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity;

    iget v1, v0, Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;->WR:I

    const/4 v2, 0x0

    if-ltz v1, :cond_3

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->gci:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Ko;

    invoke-static {v1}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1, v2}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Ko;->Fj(Ljava/lang/String;Ljava/lang/CharSequence;)V

    :cond_3
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity$6;->ex:Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity;

    iget-object v1, v0, Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object v1, v1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->kF:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;

    invoke-virtual {v1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->rf()D

    move-result-wide v7

    sub-double/2addr v7, v5

    double-to-int v1, v7

    iput v1, v0, Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;->WR:I

    long-to-int v0, v3

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/core/JU;->eV()Lcom/bytedance/sdk/openadsdk/core/settings/Ubf;

    move-result-object v1

    iget-object v3, p0, Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity$6;->ex:Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity;

    iget-object v3, v3, Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget v3, v3, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->JW:I

    invoke-static {v3}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    move-result-object v3

    invoke-interface {v1, v3}, Lcom/bytedance/sdk/openadsdk/core/settings/Ubf;->Ko(Ljava/lang/String;)I

    move-result v1

    const/4 v3, 0x1

    if-ltz v1, :cond_4

    const/4 v4, 0x1

    goto :goto_0

    :cond_4
    const/4 v4, 0x0

    :goto_0
    iget-object v5, p0, Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity$6;->ex:Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity;

    iget-object v5, v5, Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object v5, v5, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->fj:Ljava/util/concurrent/atomic/AtomicBoolean;

    invoke-virtual {v5}, Ljava/util/concurrent/atomic/AtomicBoolean;->get()Z

    move-result v5

    if-nez v5, :cond_5

    iget-object v5, p0, Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity$6;->ex:Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity;

    iget-object v5, v5, Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object v5, v5, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->mC:Ljava/util/concurrent/atomic/AtomicBoolean;

    invoke-virtual {v5}, Ljava/util/concurrent/atomic/AtomicBoolean;->get()Z

    move-result v5

    if-eqz v5, :cond_6

    :cond_5
    iget-object v5, p0, Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity$6;->ex:Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity;

    iget-object v5, v5, Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object v5, v5, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->kF:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;

    invoke-virtual {v5}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->ex()Z

    move-result v5

    if-eqz v5, :cond_6

    iget-object v5, p0, Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity$6;->ex:Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity;

    iget-object v5, v5, Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object v5, v5, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->kF:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;

    invoke-virtual {v5}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->JW()V

    :cond_6
    iget-object v5, p0, Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity$6;->ex:Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity;

    iget-object v5, v5, Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object v5, v5, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Obv:Lcom/bytedance/sdk/openadsdk/component/reward/view/Ubf;

    invoke-virtual {v5, v0}, Lcom/bytedance/sdk/openadsdk/component/reward/view/Ubf;->Ubf(I)V

    iget-object v5, p0, Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity$6;->ex:Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity;

    invoke-virtual {v5, p1, p2, p3, p4}, Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity;->Fj(JJ)V

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity$6;->ex:Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity;

    iget p2, p1, Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;->WR:I

    if-lez p2, :cond_8

    iget-object p1, p1, Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object p1, p1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->gci:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Ko;

    invoke-virtual {p1, v3}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Ko;->eV(Z)V

    if-eqz v4, :cond_7

    if-lt v0, v1, :cond_7

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity$6;->ex:Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity;

    iget-object p1, p1, Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    invoke-virtual {p1, v3}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Fj(Z)V

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity$6;->ex:Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity;

    iget-object p2, p1, Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object p2, p2, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->gci:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Ko;

    iget p1, p1, Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;->WR:I

    invoke-static {p1}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    move-result-object p1

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/common/TTAdDislikeToast;->getSkipText()Ljava/lang/String;

    move-result-object p3

    invoke-virtual {p2, p1, p3}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Ko;->Fj(Ljava/lang/String;Ljava/lang/CharSequence;)V

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity$6;->ex:Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity;

    iget-object p1, p1, Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object p1, p1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->gci:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Ko;

    invoke-virtual {p1, v3}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Ko;->Ubf(Z)V

    return-void

    :cond_7
    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity$6;->ex:Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity;

    iget-object p2, p1, Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object p2, p2, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->gci:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Ko;

    iget p1, p1, Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;->WR:I

    invoke-static {p1}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p2, p1, v2}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Ko;->Fj(Ljava/lang/String;Ljava/lang/CharSequence;)V

    :cond_8
    return-void
.end method

.method public ex(JI)V
    .locals 1

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity$6;->ex:Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity;

    iget-object p1, p1, Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;->eV:Lcom/bytedance/sdk/component/utils/Vq;

    const/16 p2, 0x12c

    invoke-virtual {p1, p2}, Landroid/os/Handler;->removeMessages(I)V

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity$6;->ex:Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity;

    iget-object p1, p1, Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object p1, p1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->kF:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->ex()Z

    move-result p1

    if-eqz p1, :cond_0

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity$6;->ex:Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity;

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;->UYd()V

    return-void

    :cond_0
    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity$6;->ex:Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity;

    iget-object p1, p1, Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object p1, p1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->kF:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->dG()V

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity$6;->ex:Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity;

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity;->mE()V

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity$6;->ex:Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity;

    iget-object p2, p1, Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-boolean p2, p2, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->ex:Z

    const/4 p3, 0x1

    if-eqz p2, :cond_1

    const/4 p2, 0x0

    const/4 v0, 0x3

    invoke-virtual {p1, p2, p3, v0}, Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;->Fj(ZZI)V

    goto :goto_0

    :cond_1
    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity;->finish()V

    :goto_0
    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity$6;->ex:Lcom/bytedance/sdk/openadsdk/activity/TTRewardVideoActivity;

    iget-object p1, p1, Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object p1, p1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->kF:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->Moo()Z

    move-result p2

    xor-int/2addr p2, p3

    const/4 p3, 0x2

    invoke-virtual {p1, p2, p3}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->Fj(II)V

    return-void
.end method
